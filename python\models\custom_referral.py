"""
Custom Referral model for managing custom referral links
Maintains identical functionality to PHP version
"""

import logging
from typing import Dict, Any, List
from utils.helpers import get_current_timestamp
from telegram import InlineKeyboardButton, InlineKeyboardMarkup

logger = logging.getLogger(__name__)

class CustomReferralModel:
    """Model for custom referral operations"""
    
    @staticmethod
    def create_custom_referral_data(
        custom_param: str,
        user_id: int,
        user_name: str,
        username: str,
        created_by: int
    ) -> Dict[str, Any]:
        """Create custom referral data structure (matching PHP structure exactly)"""
        return {
            'custom_param': custom_param,
            'user_id': user_id,
            'user_name': user_name,
            'username': username,
            'created_by': created_by,
            'created_at': get_current_timestamp(),
            'updated_at': get_current_timestamp(),
            'clicks': 0,
            'referrals': 0,
            'active': True,
            'last_clicked': None,
            'last_referral': None
        }
    
    @staticmethod
    def format_custom_referral_list_message(referrals: List[Dict[str, Any]], page: int = 1, per_page: int = 10) -> str:
        """Format custom referral list message for admin with pagination support"""
        try:
            if not referrals:
                message = "🔗 <b>Custom Referral Links</b>\n\n"
                message += "❌ No custom referral links found.\n\n"
                message += "Use the commands below to create and manage custom referral links."
                return message

            # Sort by creation date (newest first)
            sorted_referrals = sorted(referrals, key=lambda x: x.get('created_at', 0), reverse=True)

            # Calculate pagination
            total_links = len(sorted_referrals)
            total_pages = (total_links + per_page - 1) // per_page  # Ceiling division
            start_index = (page - 1) * per_page
            end_index = start_index + per_page
            page_referrals = sorted_referrals[start_index:end_index]

            # Build message header
            message = "🔗 <b>Custom Referral Links</b>\n\n"
            message += f"📊 <b>Total Links:</b> {total_links}\n"
            message += f"📄 <b>Page {page} of {total_pages}</b>\n\n"

            # Display links for current page
            for i, referral in enumerate(page_referrals, start_index + 1):
                param = referral.get('custom_param', 'Unknown')
                user_name = referral.get('user_name', 'Unknown')
                user_id = referral.get('user_id', 'Unknown')
                clicks = referral.get('clicks', 0)
                conversions = referral.get('referrals', 0)
                active = referral.get('active', True)

                status_emoji = "✅" if active else "❌"

                message += f"<b>{i}.</b> {status_emoji} <code>{param}</code>\n"
                message += f"   👤 {user_name} (ID: {user_id})\n"
                message += f"   📊 {clicks} clicks, {conversions} referrals\n\n"
            
            message += "💡 <b>Commands:</b>\n"
            message += "• <code>/customref create [param] [user_id]</code>\n"
            message += "• <code>/customref edit [old] [new]</code>\n"
            message += "• <code>/customref delete [param]</code>"
            
            return message
            
        except Exception as e:
            logger.error(f"Error formatting custom referral list: {e}")
            return "🔗 <b>Custom Referral Links</b>\n\n❌ Error loading referral links."
    
    @staticmethod
    def format_custom_referral_help_message() -> str:
        """Format custom referral help message (matching PHP version exactly)"""
        message = "🔗 <b>Custom Referral Link Management</b>\n\n"
        message += "📋 <b>Available Commands:</b>\n\n"
        
        message += "<code>/customref list</code>\n"
        message += "• Show all custom referral links\n\n"
        
        message += "<code>/customref create [parameter] [user_id]</code>\n"
        message += "• Create new custom link\n"
        message += "• Example: <code>/customref create premium-offer 123456789</code>\n\n"
        
        message += "<code>/customref edit [old_param] [new_param]</code>\n"
        message += "• Update existing custom parameter\n"
        message += "• Example: <code>/customref edit old-param new-param</code>\n\n"
        
        message += "<code>/customref delete [parameter]</code>\n"
        message += "• Delete custom referral link\n"
        message += "• Example: <code>/customref delete premium-offer</code>\n\n"
        
        message += "📝 <b>Parameter Rules:</b>\n"
        message += "• 3-50 characters long\n"
        message += "• Letters, numbers, hyphens, underscores only\n"
        message += "• No reserved words (start, help, admin, etc.)\n\n"
        
        message += "📊 <b>Tracking:</b>\n"
        message += "• Clicks and conversions are automatically tracked\n"
        message += "• View statistics with <code>/customref list</code>"
        
        return message
    
    @staticmethod
    def format_custom_referral_creation_success_message(
        custom_param: str,
        custom_link: str,
        user_name: str,
        user_id: int
    ) -> str:
        """Format custom referral creation success message (matching PHP version exactly)"""
        message = "✅ <b>Custom Referral Link Created</b>\n\n"
        message += f"👤 <b>User:</b> {user_name} (ID: <code>{user_id}</code>)\n"
        message += f"🔗 <b>Custom Parameter:</b> <code>{custom_param}</code>\n"
        message += f"🌐 <b>Custom Link:</b>\n<code>{custom_link}</code>\n\n"
        message += f"📋 <i>Users who click this link will be credited as referrals to {user_name}</i>\n\n"
        message += "💡 <b>Tip:</b> Share this link to track specific campaigns or sources"
        
        return message
    
    @staticmethod
    def format_custom_referral_update_success_message(
        old_param: str,
        new_param: str,
        custom_link: str,
        user_name: str,
        user_id: int
    ) -> str:
        """Format custom referral update success message (matching PHP version exactly)"""
        message = "✅ <b>Custom Referral Link Updated</b>\n\n"
        message += f"👤 <b>User:</b> {user_name} (ID: <code>{user_id}</code>)\n"
        message += f"🔄 <b>Changed:</b> <code>{old_param}</code> → <code>{new_param}</code>\n"
        message += f"🌐 <b>New Link:</b>\n<code>{custom_link}</code>\n\n"
        message += f"📋 <i>The old link will no longer work. Use the new link above.</i>"
        
        return message
    
    @staticmethod
    def format_custom_referral_deletion_success_message(
        param: str,
        user_name: str,
        user_id: int
    ) -> str:
        """Format custom referral deletion success message (matching PHP version exactly)"""
        message = "✅ <b>Custom Referral Link Deleted</b>\n\n"
        message += f"👤 <b>User:</b> {user_name} (ID: <code>{user_id}</code>)\n"
        message += f"🗑️ <b>Deleted Parameter:</b> <code>{param}</code>\n\n"
        message += f"📋 <i>This link will no longer work and cannot be recovered.</i>"
        
        return message
    
    @staticmethod
    def create_custom_referral_management_keyboard() -> InlineKeyboardMarkup:
        """Create custom referral management keyboard (matching PHP version exactly)"""
        try:
            keyboard_buttons = [
                [
                    InlineKeyboardButton('📋 List All Links', callback_data='customref_list'),
                    InlineKeyboardButton('❓ Help & Commands', callback_data='customref_help')
                ],
                [
                    InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')
                ]
            ]

            return InlineKeyboardMarkup(keyboard_buttons)

        except Exception as e:
            logger.error(f"Error creating custom referral management keyboard: {e}")
            # Return basic keyboard if there's an error
            return InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])

    @staticmethod
    def create_custom_referral_list_keyboard(current_page: int, total_pages: int) -> InlineKeyboardMarkup:
        """Create paginated keyboard for custom referral list"""
        try:
            keyboard_buttons = []

            # Navigation buttons row
            nav_buttons = []

            # Previous button (only if not on first page)
            if current_page > 1:
                nav_buttons.append(
                    InlineKeyboardButton('⬅️ Previous', callback_data=f'customref_list_page_{current_page - 1}')
                )

            # Page indicator (always show)
            nav_buttons.append(
                InlineKeyboardButton(f'📄 {current_page}/{total_pages}', callback_data='customref_page_info')
            )

            # Next button (only if not on last page)
            if current_page < total_pages:
                nav_buttons.append(
                    InlineKeyboardButton('➡️ Next', callback_data=f'customref_list_page_{current_page + 1}')
                )

            # Add navigation row if there are navigation buttons
            if nav_buttons:
                keyboard_buttons.append(nav_buttons)

            # Management buttons row
            keyboard_buttons.append([
                InlineKeyboardButton('❓ Help & Commands', callback_data='customref_help')
            ])

            # Back button row
            keyboard_buttons.append([
                InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')
            ])

            return InlineKeyboardMarkup(keyboard_buttons)

        except Exception as e:
            logger.error(f"Error creating custom referral list keyboard: {e}")
            # Return basic keyboard if there's an error
            return InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])
    
    @staticmethod
    def create_custom_referral_success_keyboard() -> InlineKeyboardMarkup:
        """Create custom referral success keyboard (matching PHP version exactly)"""
        try:
            keyboard_buttons = [
                [
                    InlineKeyboardButton('🔗 Create Another Link', callback_data='customReferralLinks'),
                    InlineKeyboardButton('📋 View All Links', callback_data='customref_list')
                ],
                [
                    InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')
                ]
            ]
            
            return InlineKeyboardMarkup(keyboard_buttons)
            
        except Exception as e:
            logger.error(f"Error creating custom referral success keyboard: {e}")
            # Return basic keyboard if there's an error
            return InlineKeyboardMarkup([
                [InlineKeyboardButton('↩️ Back to Admin Panel', callback_data='admin')]
            ])
    
    @staticmethod
    def parse_custom_referral_command(command_text: str) -> Dict[str, Any]:
        """Parse custom referral command (matching PHP logic exactly)"""
        try:
            # Remove /customref prefix and split by spaces
            parts = command_text.replace('/customref', '').strip().split()
            
            if not parts:
                return {
                    'valid': False,
                    'action': None,
                    'message': 'No action specified. Use /customref help for available commands.'
                }
            
            action = parts[0].lower()
            
            if action == 'list':
                return {
                    'valid': True,
                    'action': 'list',
                    'message': 'List all custom referral links.'
                }
            
            elif action == 'help':
                return {
                    'valid': True,
                    'action': 'help',
                    'message': 'Show help for custom referral commands.'
                }
            
            elif action == 'create':
                if len(parts) < 3:
                    return {
                        'valid': False,
                        'action': 'create',
                        'message': 'Usage: /customref create [parameter] [user_id]\nExample: /customref create premium-offer 123456789'
                    }
                
                try:
                    param = parts[1]
                    user_id = int(parts[2])
                    
                    return {
                        'valid': True,
                        'action': 'create',
                        'param': param,
                        'user_id': user_id,
                        'message': f'Create custom referral link "{param}" for user {user_id}.'
                    }
                except ValueError:
                    return {
                        'valid': False,
                        'action': 'create',
                        'message': 'Invalid user ID. Please provide a valid number.'
                    }
            
            elif action == 'edit':
                if len(parts) < 3:
                    return {
                        'valid': False,
                        'action': 'edit',
                        'message': 'Usage: /customref edit [old_param] [new_param]\nExample: /customref edit old-param new-param'
                    }
                
                old_param = parts[1]
                new_param = parts[2]
                
                return {
                    'valid': True,
                    'action': 'edit',
                    'old_param': old_param,
                    'new_param': new_param,
                    'message': f'Update custom referral parameter from "{old_param}" to "{new_param}".'
                }
            
            elif action == 'delete':
                if len(parts) < 2:
                    return {
                        'valid': False,
                        'action': 'delete',
                        'message': 'Usage: /customref delete [parameter]\nExample: /customref delete premium-offer'
                    }
                
                param = parts[1]
                
                return {
                    'valid': True,
                    'action': 'delete',
                    'param': param,
                    'message': f'Delete custom referral link "{param}".'
                }
            
            else:
                return {
                    'valid': False,
                    'action': None,
                    'message': f'Unknown action "{action}". Use /customref help for available commands.'
                }
            
        except Exception as e:
            logger.error(f"Error parsing custom referral command: {e}")
            return {
                'valid': False,
                'action': None,
                'message': 'Error parsing command. Use /customref help for available commands.'
            }
