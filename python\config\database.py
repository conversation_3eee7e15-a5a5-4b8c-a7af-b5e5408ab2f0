"""
MongoDB database connection and configuration
Maintains identical data structures to PHP JSON files
"""

import asyncio
import logging
from typing import Optional
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError
from config.settings import settings

logger = logging.getLogger(__name__)

class DatabaseManager:
    """MongoDB database manager with connection pooling"""
    
    def __init__(self):
        self.client: Optional[AsyncIOMotorClient] = None
        self.database: Optional[AsyncIOMotorDatabase] = None
        self._connected = False
    
    async def connect(self) -> bool:
        """Establish connection to MongoDB Atlas"""
        try:
            # Create MongoDB client with connection pooling
            self.client = AsyncIOMotorClient(
                settings.MONGODB_URI,
                maxPoolSize=50,
                minPoolSize=10,
                maxIdleTimeMS=30000,
                serverSelectionTimeoutMS=5000,
                connectTimeoutMS=10000,
                socketTimeoutMS=20000,
                retryWrites=True,
                w='majority'
            )
            
            # Get database
            self.database = self.client[settings.DATABASE_NAME]
            
            # Test connection
            await self.client.admin.command('ping')
            
            self._connected = True
            logger.info(f"Successfully connected to MongoDB: {settings.DATABASE_NAME}")
            
            # Initialize collections and indexes
            await self._initialize_collections()
            
            return True
            
        except (ConnectionFailure, ServerSelectionTimeoutError) as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            self._connected = False
            return False
        except Exception as e:
            logger.error(f"Unexpected error connecting to MongoDB: {e}")
            self._connected = False
            return False
    
    async def disconnect(self):
        """Close database connection"""
        if self.client:
            self.client.close()
            self._connected = False
            logger.info("Disconnected from MongoDB")
    
    async def _initialize_collections(self):
        """Initialize collections and create indexes for optimal performance"""
        try:
            # Users collection indexes
            await self.database.users.create_index("user_id", unique=True)
            await self.database.users.create_index("username")
            await self.database.users.create_index("referred_by")
            await self.database.users.create_index("banned")
            await self.database.users.create_index("balance")
            
            # Admin settings collection indexes
            await self.database.admin_settings.create_index("admin_id", unique=True)
            
            # Tasks collection indexes
            await self.database.tasks.create_index("task_id", unique=True)
            await self.database.tasks.create_index("status")
            await self.database.tasks.create_index("created_by")
            
            # Task submissions collection indexes
            await self.database.task_submissions.create_index("submission_id", unique=True)
            await self.database.task_submissions.create_index("user_id")
            await self.database.task_submissions.create_index("task_id")
            await self.database.task_submissions.create_index("status")
            
            # Gift codes collection indexes
            await self.database.gift_codes.create_index("code", unique=True)
            await self.database.gift_codes.create_index("status")

            # Link-based codes collection indexes
            await self.database.link_based_codes.create_index("code", unique=True)
            await self.database.link_based_codes.create_index("is_active")
            await self.database.link_based_codes.create_index("created_by")
            await self.database.link_based_codes.create_index("created_at")

            # Custom referrals collection indexes
            await self.database.custom_referrals.create_index("custom_param", unique=True)
            await self.database.custom_referrals.create_index("user_id")
            
            # Sessions collection indexes (with TTL for auto-cleanup)
            await self.database.sessions.create_index("user_id", unique=True)
            await self.database.sessions.create_index(
                "created_at", 
                expireAfterSeconds=3600  # Auto-delete sessions after 1 hour
            )
            
            # Broadcast logs collection indexes
            await self.database.broadcast_logs.create_index("broadcast_id", unique=True)
            await self.database.broadcast_logs.create_index("admin_id")
            await self.database.broadcast_logs.create_index("created_at")
            
            # Rate limits collection indexes (with TTL)
            await self.database.rate_limits.create_index("user_id", unique=True)
            await self.database.rate_limits.create_index(
                "last_request", 
                expireAfterSeconds=60  # Auto-delete rate limit records after 1 minute
            )
            
            logger.info("Database collections and indexes initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing database collections: {e}")
    
    @property
    def is_connected(self) -> bool:
        """Check if database is connected"""
        return self._connected
    
    def get_collection(self, collection_name: str):
        """Get a collection from the database"""
        if not self.database:
            raise RuntimeError("Database not connected")
        return self.database[collection_name]

# Global database manager instance
db_manager = DatabaseManager()

# Collection name constants (matching PHP JSON file names)
COLLECTIONS = {
    'users': 'users',
    'admin_settings': 'admin_settings',
    'tasks': 'tasks',
    'task_submissions': 'task_submissions',
    'gift_codes': 'gift_codes',
    'link_based_codes': 'link_based_codes',
    'custom_referrals': 'custom_referrals',
    'sessions': 'sessions',
    'broadcast_logs': 'broadcast_logs',
    'broadcast_sessions': 'broadcast_sessions',
    'gift_broadcasts': 'gift_broadcasts',
    'gift_broadcast_campaigns': 'gift_broadcast_campaigns',
    'admin_logs': 'admin_logs',
    'force_channels': 'force_channels',
    'rate_limits': 'rate_limits',
    'bot_info': 'bot_info',
    'withdrawals': 'withdrawals'
}

async def get_database() -> AsyncIOMotorDatabase:
    """Get database instance"""
    if not db_manager.is_connected:
        await db_manager.connect()
    return db_manager.database

async def get_collection(collection_name: str):
    """Get a specific collection with error handling"""
    try:
        database = await get_database()
        if database is None:
            logger.error("Database connection is None - cannot get collection")
            return None
        return database[collection_name]
    except Exception as e:
        logger.error(f"Error getting collection {collection_name}: {e}")
        return None
