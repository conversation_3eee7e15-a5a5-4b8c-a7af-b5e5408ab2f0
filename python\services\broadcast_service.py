"""
Broadcast service for sending messages to all users
Maintains identical functionality to PHP version
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
from telegram import Bo<PERSON>
from telegram.error import TelegramError

from config.database import get_collection, COLLECTIONS
from config.settings import settings
from utils.helpers import get_current_timestamp, generate_broadcast_id
from config.broadcast_config import performance_monitor

logger = logging.getLogger(__name__)

class BroadcastService:
    """Service for broadcasting messages to users"""
    
    def __init__(self):
        self.bot = None
    
    def set_bot(self, bot: Bot):
        """Set the bot instance for broadcasting"""
        self.bot = bot
    
    async def broadcast_text_message(self, message: str, admin_id: int, exclude_banned: bool = True) -> Dict[str, Any]:
        """Broadcast text message to all users"""
        try:
            if not self.bot:
                return {
                    'success': False,
                    'message': 'Bot instance not set'
                }
            
            # Get all users
            users_collection = await get_collection(COLLECTIONS['users'])
            
            # Build query
            query = {}
            if exclude_banned:
                query['banned'] = {'$ne': True}
            
            cursor = users_collection.find(query, {'user_id': 1})
            users = await cursor.to_list(length=None)
            
            total_users = len(users)
            successful_sends = 0
            failed_sends = 0
            
            logger.info(f"Starting broadcast to {total_users} users")
            
            # Send messages in batches to avoid rate limiting
            batch_size = 30  # Telegram rate limit
            delay_between_batches = 1  # 1 second delay
            
            for i in range(0, len(users), batch_size):
                batch = users[i:i + batch_size]
                
                # Send to batch
                for user in batch:
                    try:
                        await self.bot.send_message(
                            chat_id=user['user_id'],
                            text=message,
                            parse_mode='HTML'
                        )
                        successful_sends += 1
                        
                        # Small delay between individual messages
                        await asyncio.sleep(0.05)
                        
                    except TelegramError as e:
                        failed_sends += 1
                        logger.warning(f"Failed to send broadcast to {user['user_id']}: {e}")
                    except Exception as e:
                        failed_sends += 1
                        logger.error(f"Error sending broadcast to {user['user_id']}: {e}")
                
                # Delay between batches
                if i + batch_size < len(users):
                    await asyncio.sleep(delay_between_batches)
            
            # Log broadcast completion
            await self._log_broadcast(admin_id, 'text', message, total_users, successful_sends, failed_sends)
            
            return {
                'success': True,
                'total_users': total_users,
                'successful_sends': successful_sends,
                'failed_sends': failed_sends,
                'message': f'Broadcast completed. Sent to {successful_sends}/{total_users} users.'
            }
            
        except Exception as e:
            logger.error(f"Error in broadcast_text_message: {e}")
            return {
                'success': False,
                'message': f'Broadcast failed: {str(e)}'
            }
    
    async def broadcast_gift_message(self, gift_amount: float, admin_id: int, exclude_banned: bool = True) -> Dict[str, Any]:
        """Broadcast gift message and add balance to all users"""
        try:
            if not self.bot:
                return {
                    'success': False,
                    'message': 'Bot instance not set'
                }
            
            # Get all users
            users_collection = await get_collection(COLLECTIONS['users'])
            
            # Build query
            query = {}
            if exclude_banned:
                query['banned'] = {'$ne': True}
            
            cursor = users_collection.find(query, {'user_id': 1, 'first_name': 1})
            users = await cursor.to_list(length=None)
            
            total_users = len(users)
            successful_sends = 0
            failed_sends = 0
            total_gift_amount = 0
            
            logger.info(f"Starting gift broadcast to {total_users} users")
            
            # Prepare gift message
            gift_message = f"🎁 <b>Gift Alert!</b>\n\n"
            gift_message += f"You have received ₹{gift_amount} as a gift from admin!\n\n"
            gift_message += f"💰 <b>Gift Amount:</b> ₹{gift_amount}\n"
            gift_message += f"🎉 <b>Enjoy your gift!</b>"
            
            # Send messages and add balance in batches
            batch_size = 30
            delay_between_batches = 1
            
            for i in range(0, len(users), batch_size):
                batch = users[i:i + batch_size]
                
                # Process batch
                for user in batch:
                    try:
                        # Add balance to user
                        await users_collection.update_one(
                            {'user_id': user['user_id']},
                            {'$inc': {'balance': gift_amount}}
                        )
                        
                        # Send gift message
                        await self.bot.send_message(
                            chat_id=user['user_id'],
                            text=gift_message,
                            parse_mode='HTML'
                        )
                        
                        successful_sends += 1
                        total_gift_amount += gift_amount
                        
                        # Small delay between individual messages
                        await asyncio.sleep(0.05)
                        
                    except TelegramError as e:
                        failed_sends += 1
                        logger.warning(f"Failed to send gift to {user['user_id']}: {e}")
                    except Exception as e:
                        failed_sends += 1
                        logger.error(f"Error sending gift to {user['user_id']}: {e}")
                
                # Delay between batches
                if i + batch_size < len(users):
                    await asyncio.sleep(delay_between_batches)
            
            # Log broadcast completion
            await self._log_broadcast(admin_id, 'gift', f"Gift: ₹{gift_amount}", total_users, successful_sends, failed_sends)
            
            return {
                'success': True,
                'total_users': total_users,
                'successful_sends': successful_sends,
                'failed_sends': failed_sends,
                'total_gift_amount': total_gift_amount,
                'message': f'Gift broadcast completed. Sent ₹{gift_amount} to {successful_sends}/{total_users} users. Total distributed: ₹{total_gift_amount}'
            }
            
        except Exception as e:
            logger.error(f"Error in broadcast_gift_message: {e}")
            return {
                'success': False,
                'message': f'Gift broadcast failed: {str(e)}'
            }
    
    async def _log_broadcast(self, admin_id: int, broadcast_type: str, content: str, total_users: int, successful: int, failed: int):
        """Log broadcast activity"""
        try:
            logs_collection = await get_collection(COLLECTIONS['admin_logs'])
            
            log_entry = {
                'admin_id': admin_id,
                'action': f'broadcast_{broadcast_type}',
                'details': {
                    'type': broadcast_type,
                    'content': content[:100],  # Truncate long content
                    'total_users': total_users,
                    'successful_sends': successful,
                    'failed_sends': failed
                },
                'timestamp': get_current_timestamp()
            }
            
            await logs_collection.insert_one(log_entry)
            
        except Exception as e:
            logger.error(f"Error logging broadcast: {e}")
    
    async def get_broadcast_statistics(self) -> Dict[str, Any]:
        """Get broadcast statistics"""
        try:
            logs_collection = await get_collection(COLLECTIONS['admin_logs'])
            
            # Get recent broadcasts
            cursor = logs_collection.find(
                {'action': {'$in': ['broadcast_text', 'broadcast_gift']}},
                sort=[('timestamp', -1)],
                limit=10
            )
            recent_broadcasts = await cursor.to_list(length=None)
            
            # Get total broadcast count
            total_broadcasts = await logs_collection.count_documents({
                'action': {'$in': ['broadcast_text', 'broadcast_gift']}
            })
            
            return {
                'success': True,
                'total_broadcasts': total_broadcasts,
                'recent_broadcasts': recent_broadcasts
            }
            
        except Exception as e:
            logger.error(f"Error getting broadcast statistics: {e}")
            return {
                'success': False,
                'message': f'Error getting statistics: {str(e)}'
            }

    # ==================== NEW ADMIN BROADCAST SYSTEM ====================

    async def get_broadcast_draft(self, admin_id: int) -> Optional[Dict[str, Any]]:
        """Get broadcast draft for admin with enhanced error handling"""
        try:
            logger.debug(f"Getting broadcast draft for admin {admin_id}")
            collection = await get_collection(COLLECTIONS['broadcast_sessions'])

            if collection is None:
                logger.error(f"Database collection is None - cannot get draft for admin {admin_id}")
                return None

            draft = await collection.find_one({"admin_id": admin_id, "status": "draft"})
            logger.debug(f"Found draft for admin {admin_id}: {draft is not None}")
            return draft
        except Exception as e:
            logger.error(f"Error getting broadcast draft for admin {admin_id}: {e}")
            return None

    async def save_broadcast_draft(self, admin_id: int, data: Dict[str, Any]) -> bool:
        """Save or update broadcast draft with enhanced error handling"""
        try:
            logger.info(f"=== SAVING BROADCAST DRAFT for admin {admin_id} ===")
            logger.info(f"Data to save: {data}")

            collection = await get_collection(COLLECTIONS['broadcast_sessions'])

            if collection is None:
                logger.error(f"Database collection is None - cannot save draft for admin {admin_id}")
                return False

            logger.info(f"Got collection: {collection}")

            # Update existing draft or create new one
            logger.info(f"Attempting to update/upsert broadcast draft for admin {admin_id}")

            # Check if this is a clear operation (empty data)
            is_clear_operation = len(data) == 0

            if is_clear_operation:
                # For clear operations, explicitly unset content fields and set required fields
                logger.info(f"Clear operation detected - removing all content fields for admin {admin_id}")
                result = await collection.update_one(
                    {"admin_id": admin_id, "status": "draft"},
                    {
                        "$set": {
                            "admin_id": admin_id,
                            "status": "draft",
                            "updated_at": get_current_timestamp()
                        },
                        "$unset": {
                            "text": "",
                            "media": "",
                            "buttons": ""
                        }
                    },
                    upsert=True
                )
            else:
                # For normal save operations, use $set as before
                result = await collection.update_one(
                    {"admin_id": admin_id, "status": "draft"},
                    {
                        "$set": {
                            **data,
                            "admin_id": admin_id,
                            "status": "draft",
                            "updated_at": get_current_timestamp()
                        }
                    },
                    upsert=True
                )

            logger.info(f"=== BROADCAST DRAFT SAVE RESULT for admin {admin_id} ===")
            logger.info(f"Acknowledged: {result.acknowledged}")
            logger.info(f"Matched count: {result.matched_count}")
            logger.info(f"Modified count: {result.modified_count}")
            logger.info(f"Upserted ID: {result.upserted_id}")

            success = result.acknowledged
            logger.info(f"Returning success: {success}")
            return success

        except Exception as e:
            logger.error(f"=== ERROR SAVING BROADCAST DRAFT for admin {admin_id}: {e} ===")
            logger.error(f"Exception type: {type(e)}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return False

    async def clear_broadcast_draft_completely(self, admin_id: int) -> bool:
        """Completely clear broadcast draft by removing all content fields"""
        try:
            logger.info(f"Completely clearing broadcast draft for admin {admin_id}")
            collection = await get_collection(COLLECTIONS['broadcast_sessions'])

            if collection is None:
                logger.error(f"Database collection is None - cannot clear draft for admin {admin_id}")
                return False

            # Use $unset to explicitly remove all content fields
            result = await collection.update_one(
                {"admin_id": admin_id, "status": "draft"},
                {
                    "$set": {
                        "admin_id": admin_id,
                        "status": "draft",
                        "updated_at": get_current_timestamp()
                    },
                    "$unset": {
                        "text": "",
                        "media": "",
                        "buttons": ""
                    }
                },
                upsert=True
            )

            logger.info(f"Clear draft result for admin {admin_id}: acknowledged={result.acknowledged}, modified={result.modified_count}")
            return result.acknowledged

        except Exception as e:
            logger.error(f"Error completely clearing broadcast draft for admin {admin_id}: {e}")
            return False

    async def clear_broadcast_draft(self, admin_id: int) -> bool:
        """Clear broadcast draft (alias for clear_broadcast_draft_completely)"""
        return await self.clear_broadcast_draft_completely(admin_id)

    async def clear_broadcast_content(self, admin_id: int, content_type: str = None) -> bool:
        """Clear specific content type or all content from broadcast draft"""
        try:
            logger.info(f"Clearing broadcast content for admin {admin_id}, type: {content_type}")

            # Get current draft
            draft = await self.get_broadcast_draft(admin_id)
            if not draft:
                logger.info(f"No draft found for admin {admin_id}")
                return True  # Nothing to clear

            # Clear specific content type or all content
            if content_type == 'text' and 'text' in draft:
                del draft['text']
                logger.info(f"Cleared text content for admin {admin_id}")
            elif content_type == 'media' and 'media' in draft:
                del draft['media']
                logger.info(f"Cleared media content for admin {admin_id}")
            elif content_type == 'buttons' and 'buttons' in draft:
                del draft['buttons']
                logger.info(f"Cleared button content for admin {admin_id}")
            elif content_type is None:
                # Clear all content
                draft = {}
                logger.info(f"Cleared all content for admin {admin_id}")
            else:
                logger.warning(f"Invalid content type '{content_type}' or content not found")
                return False

            # Save updated draft
            success = await self.save_broadcast_draft(admin_id, draft)
            logger.info(f"Content clear result for admin {admin_id}: {success}")
            return success

        except Exception as e:
            logger.error(f"Error clearing broadcast content for admin {admin_id}: {e}")
            return False

    async def start_broadcast(self, admin_id: int, broadcast_data: Dict[str, Any]) -> Optional[str]:
        """Initialize broadcast and return broadcast ID"""
        try:
            broadcast_id = generate_broadcast_id()

            # Get total user count
            users_collection = await get_collection(COLLECTIONS['users'])
            total_users = await users_collection.count_documents({})

            # Create broadcast log entry
            broadcast_log = {
                "broadcast_id": broadcast_id,
                "admin_id": admin_id,
                "status": "in_progress",
                "broadcast_data": broadcast_data,
                "total_users": total_users,
                "successful_sends": 0,
                "failed_sends": 0,
                "blocked_users": 0,
                "started_at": get_current_timestamp(),
                "completed_at": None
            }

            collection = await get_collection(COLLECTIONS['broadcast_logs'])
            result = await collection.insert_one(broadcast_log)

            if result.inserted_id:
                # Log broadcast initiation
                from services.admin_logging_service import AdminLoggingService
                logging_service = AdminLoggingService()
                await logging_service.log_broadcast_started(
                    admin_id,
                    broadcast_id,
                    "user_broadcast",
                    total_users
                )
                return broadcast_id

            return None

        except Exception as e:
            logger.error(f"Error starting broadcast: {e}")
            return None

    async def execute_broadcast(self, broadcast_id: str, query) -> None:
        """Execute broadcast with real-time progress updates"""
        try:
            from telegram import CallbackQuery

            # Get broadcast details
            collection = await get_collection(COLLECTIONS['broadcast_logs'])
            broadcast = await collection.find_one({"broadcast_id": broadcast_id})

            if not broadcast:
                await query.edit_message_text("❌ Broadcast not found.")
                return

            # Get all users
            users_collection = await get_collection(COLLECTIONS['users'])
            cursor = users_collection.find({})
            users = await cursor.to_list(length=None)

            total_users = len(users)
            successful_sends = 0
            failed_sends = 0
            blocked_users = 0

            broadcast_data = broadcast['broadcast_data']

            # Initialize bot if not set
            if not self.bot:
                from config.settings import settings
                from telegram import Bot
                self.bot = Bot(settings.BOT_TOKEN)

            # Process users in batches
            batch_size = 10
            for i in range(0, total_users, batch_size):
                batch = users[i:i + batch_size]

                # Process batch
                for user in batch:
                    try:
                        user_id = user['user_id']

                        # Prepare message for user
                        message_text = broadcast_data.get('text', '')
                        if message_text:
                            # Replace placeholders
                            message_text = message_text.replace('%firstname%', user.get('first_name', 'User'))
                            message_text = message_text.replace('%username%', user.get('username', 'N/A'))
                            message_text = message_text.replace('%mention%', f"@{user.get('username', 'user')}")

                        # Create inline keyboard if buttons are configured
                        reply_markup = None
                        if broadcast_data.get('buttons'):
                            reply_markup = self._create_inline_keyboard(broadcast_data['buttons'])
                        elif broadcast_data.get('reply_markup'):
                            # Handle pre-built reply_markup (for gift broadcasts)
                            from telegram import InlineKeyboardMarkup
                            if isinstance(broadcast_data['reply_markup'], dict):
                                reply_markup = InlineKeyboardMarkup.de_json(broadcast_data['reply_markup'], None)
                            else:
                                reply_markup = broadcast_data['reply_markup']

                        # Send message
                        if broadcast_data.get('media'):
                            # Send media message
                            media_data = broadcast_data['media']
                            if media_data['type'] == 'photo':
                                await self.bot.send_photo(
                                    chat_id=user_id,
                                    photo=media_data['file_id'],
                                    caption=message_text if message_text else None,
                                    reply_markup=reply_markup,
                                    parse_mode='HTML'
                                )
                            elif media_data['type'] == 'video':
                                await self.bot.send_video(
                                    chat_id=user_id,
                                    video=media_data['file_id'],
                                    caption=message_text if message_text else None,
                                    reply_markup=reply_markup,
                                    parse_mode='HTML'
                                )
                            # Add other media types as needed
                        else:
                            # Send text message
                            await self.bot.send_message(
                                chat_id=user_id,
                                text=message_text,
                                reply_markup=reply_markup,
                                parse_mode='HTML'
                            )

                        successful_sends += 1

                    except Exception as e:
                        error_str = str(e).lower()
                        if "blocked" in error_str or "user is deactivated" in error_str or "chat not found" in error_str:
                            blocked_users += 1
                        else:
                            failed_sends += 1
                        logger.warning(f"Failed to send to user {user.get('user_id')}: {e}")

                # Update progress every batch
                users_left = total_users - (successful_sends + failed_sends + blocked_users)
                progress_message = f"⏳ Sleeping for 1 seconds\n\n"
                progress_message += f"✅ Broadcasted To: {successful_sends}\n"
                progress_message += f"🗨 Users Left: {users_left}"

                try:
                    await query.edit_message_text(progress_message)
                except:
                    pass  # Ignore edit errors

                # Small delay between batches
                await asyncio.sleep(1)

            # Calculate final statistics
            success_rate = round((successful_sends / total_users) * 100, 2) if total_users > 0 else 0

            # Update broadcast log
            await collection.update_one(
                {"broadcast_id": broadcast_id},
                {
                    "$set": {
                        "status": "completed",
                        "successful_sends": successful_sends,
                        "failed_sends": failed_sends,
                        "blocked_users": blocked_users,
                        "success_rate": success_rate,
                        "completed_at": get_current_timestamp()
                    }
                }
            )

            # Log broadcast completion
            from services.admin_logging_service import AdminLoggingService
            logging_service = AdminLoggingService()
            await logging_service.log_broadcast_completed(
                broadcast['admin_id'],
                broadcast_id,
                {
                    "total_users": total_users,
                    "successful_sends": successful_sends,
                    "failed_sends": failed_sends,
                    "blocked_users": blocked_users,
                    "success_rate": success_rate
                }
            )

            # Show final results
            final_message = "✅ Message Broadcast Completed!\n\n"
            final_message += f"🆔 Broadcast ID: {broadcast_id}\n"
            final_message += f"👥 Total Users: {total_users}\n"
            final_message += f"✅ Successfully Sent: {successful_sends}\n"
            final_message += f"❌ Failed: {failed_sends}\n"
            final_message += f"🚫 Blocked Users: {blocked_users}\n"
            final_message += f"📊 Success Rate: {success_rate}%\n"
            final_message += f"⏱️ Duration: Completed\n\n"
            final_message += "🎉 Broadcast completed successfully!"

            await query.edit_message_text(final_message)

        except Exception as e:
            logger.error(f"Error executing broadcast: {e}")
            await query.edit_message_text(
                f"❌ <b>Broadcast Error</b>\n\nSomething went wrong during broadcast execution."
            )

    # ==================== OPTIMIZED BROADCAST SYSTEM FOR 50K+ USERS ====================

    async def execute_broadcast_optimized(self, broadcast_id: str, admin_id: int, progress_callback=None) -> Dict[str, Any]:
        """Execute broadcast with optimized performance for 50k+ users"""
        try:
            # Get broadcast details
            collection = await get_collection(COLLECTIONS['broadcast_logs'])
            broadcast = await collection.find_one({"broadcast_id": broadcast_id})

            if not broadcast:
                return {"success": False, "error": "Broadcast not found"}

            # Initialize bot if not set
            if not self.bot:
                from config.settings import settings
                from telegram import Bot
                self.bot = Bot(settings.BOT_TOKEN)

            # Get all active users with optimized query
            users_collection = await get_collection(COLLECTIONS['users'])

            # Only get necessary fields to reduce memory usage
            total_users = await users_collection.count_documents({"banned": {"$ne": True}})

            # Statistics tracking
            stats = {
                "total_users": total_users,
                "successful_sends": 0,
                "failed_sends": 0,
                "blocked_users": 0,
                "deactivated_users": 0,
                "chat_not_found": 0,
                "rate_limited": 0,
                "other_errors": 0,
                "cleaned_users": []
            }

            broadcast_data = broadcast['broadcast_data']

            # Get optimized configuration based on user count
            from config.broadcast_config import BroadcastConfig
            config = BroadcastConfig.get_config_for_user_count(total_users)

            batch_size = config["batch_size"]
            delay_between_batches = config["delay_between_batches"]
            progress_update_interval = config["progress_update_interval"]
            concurrent_batches = config["concurrent_batches"]
            delay_between_messages = config["delay_between_messages"]

            # Start performance monitoring
            performance_monitor.start_monitoring(total_users)

            processed_count = 0
            start_time = get_current_timestamp()

            logger.info(f"Starting optimized broadcast {broadcast_id} to {total_users} users")

            # Process users with concurrent batches for maximum performance
            # This method now handles all batches including remaining ones internally
            processed_count = await self._process_users_concurrent_batches(
                users_collection, broadcast_id, broadcast_data, stats,
                batch_size, concurrent_batches, delay_between_batches,
                delay_between_messages, progress_update_interval, progress_callback, start_time
            )

            # Note: All batches including remaining ones are now processed within _process_users_concurrent_batches
            # No need to process remaining batch here as it's handled internally

            # Final progress update
            if progress_callback:
                await progress_callback(stats, processed_count, total_users, start_time)

            # Clean up blocked/deactivated users
            if stats["cleaned_users"]:
                await self._cleanup_inactive_users(stats["cleaned_users"])
                logger.info(f"Cleaned up {len(stats['cleaned_users'])} inactive users")

            # Final update
            await self._finalize_broadcast(broadcast_id, stats)

            # Log final performance summary
            performance_monitor.update_progress(
                stats["successful_sends"] + stats["failed_sends"],
                stats["successful_sends"],
                stats["failed_sends"]
            )
            performance_monitor.log_performance_summary()

            logger.info(f"Broadcast {broadcast_id} completed: {stats['successful_sends']}/{total_users} sent")

            return {
                "success": True,
                "statistics": stats,
                "message": f"Broadcast completed successfully. Sent to {stats['successful_sends']}/{total_users} users."
            }

        except Exception as e:
            logger.error(f"Error executing optimized broadcast {broadcast_id}: {e}")
            return {"success": False, "error": str(e)}

    async def _is_broadcast_cancelled(self, broadcast_id: str) -> bool:
        """Check if broadcast has been cancelled"""
        try:
            # First check broadcast_sessions collection
            sessions_collection = await get_collection(COLLECTIONS['broadcast_sessions'])
            session = await sessions_collection.find_one({"broadcast_id": broadcast_id})

            if session:
                # If session exists, check its cancellation status
                return session.get("cancelled", False) or session.get("status") == "cancelled"

            # If no session found, check broadcast_logs collection
            logs_collection = await get_collection(COLLECTIONS['broadcast_logs'])
            broadcast_log = await logs_collection.find_one({"broadcast_id": broadcast_id})

            if broadcast_log:
                # If broadcast log exists, check its status
                status = broadcast_log.get("status", "pending")
                return status == "cancelled"

            # If neither exists, do NOT cancel - let broadcast continue
            # This handles cases where broadcast is created directly in logs without session
            logger.warning(f"Broadcast {broadcast_id} not found in sessions or logs, continuing broadcast")
            return False

        except Exception as e:
            logger.error(f"Error checking broadcast cancellation status: {e}")
            return False  # On error, continue broadcast

    async def _handle_broadcast_cancellation(self, broadcast_id: str, stats: Dict, processed_count: int, total_users: int):
        """Handle broadcast cancellation cleanup"""
        try:
            # Update broadcast status to cancelled
            collection = await get_collection(COLLECTIONS['broadcast_logs'])
            await collection.update_one(
                {"broadcast_id": broadcast_id},
                {
                    "$set": {
                        "status": "cancelled",
                        "cancelled_at": get_current_timestamp(),
                        "processed_users": processed_count,
                        "final_stats": stats
                    }
                }
            )

            logger.info(f"Broadcast {broadcast_id} cancellation handled. Processed {processed_count}/{total_users} users")

        except Exception as e:
            logger.error(f"Error handling broadcast cancellation: {e}")

    async def _process_user_batch_optimized(self, user_batch: List[Dict], broadcast_data: Dict, stats: Dict, delay_between_messages: float = 0.01) -> Dict:
        """Process a batch of users with optimized error handling and user cleanup"""
        batch_results = {
            "successful_sends": 0,
            "failed_sends": 0,
            "blocked_users": 0,
            "deactivated_users": 0,
            "chat_not_found": 0,
            "rate_limited": 0,
            "other_errors": 0,
            "cleaned_users": []
        }

        # Ensure bot is initialized
        if not self.bot:
            logger.error("Bot instance not initialized in _process_user_batch_optimized")
            batch_results["other_errors"] = len(user_batch)
            return batch_results

        for user in user_batch:
            try:
                user_id = user['user_id']

                # Prepare message for user
                message_text = broadcast_data.get('text', '')
                if message_text:
                    # Replace placeholders
                    message_text = message_text.replace('%firstname%', user.get('first_name', 'User'))
                    message_text = message_text.replace('%username%', user.get('username', 'N/A'))
                    message_text = message_text.replace('%mention%', f"@{user.get('username', 'user')}")

                # Create inline keyboard if buttons are configured
                reply_markup = None
                if broadcast_data.get('buttons'):
                    reply_markup = self._create_inline_keyboard(broadcast_data['buttons'])
                elif broadcast_data.get('reply_markup'):
                    # Handle pre-built reply_markup (for gift broadcasts)
                    from telegram import InlineKeyboardMarkup
                    if isinstance(broadcast_data['reply_markup'], dict):
                        reply_markup = InlineKeyboardMarkup.de_json(broadcast_data['reply_markup'], None)
                    else:
                        reply_markup = broadcast_data['reply_markup']

                # Send message based on type
                if broadcast_data.get('media'):
                    await self._send_media_message(user_id, broadcast_data['media'], message_text, reply_markup)
                else:
                    await self.bot.send_message(
                        chat_id=user_id,
                        text=message_text,
                        reply_markup=reply_markup,
                        parse_mode='HTML'
                    )

                batch_results["successful_sends"] += 1

                # Dynamic delay based on configuration
                await asyncio.sleep(delay_between_messages)

            except Exception as e:
                error_str = str(e).lower()

                # Categorize errors for better handling and user cleanup
                if "forbidden: bot was blocked by the user" in error_str:
                    batch_results["blocked_users"] += 1
                    batch_results["cleaned_users"].append({"user_id": user_id, "reason": "blocked"})
                    # Mark user as banned in database immediately
                    await self._mark_user_as_banned(user_id, "bot_blocked")
                elif "bad request: user is deactivated" in error_str or "user is deactivated" in error_str:
                    batch_results["deactivated_users"] += 1
                    batch_results["cleaned_users"].append({"user_id": user_id, "reason": "deactivated"})
                    # Mark user as banned in database immediately
                    await self._mark_user_as_banned(user_id, "account_deactivated")
                elif "bad request: chat not found" in error_str or "chat not found" in error_str:
                    batch_results["chat_not_found"] += 1
                    batch_results["cleaned_users"].append({"user_id": user_id, "reason": "chat_not_found"})
                    # Mark user as banned in database immediately
                    await self._mark_user_as_banned(user_id, "chat_not_found")
                elif "too many requests" in error_str or "retry after" in error_str or "429" in error_str:
                    batch_results["rate_limited"] += 1
                    # Implement exponential backoff for rate limiting
                    retry_after = self._extract_retry_after_seconds(error_str)
                    backoff_time = max(retry_after, 2)  # Minimum 2 seconds
                    logger.warning(f"Rate limit hit for user {user_id}, backing off for {backoff_time}s")
                    await asyncio.sleep(backoff_time)
                else:
                    batch_results["other_errors"] += 1
                    logger.warning(f"Unhandled broadcast error for user {user_id}: {error_str}")

                batch_results["failed_sends"] += 1

                logger.warning(f"Failed to send to user {user_id}: {e}")

        # Log batch completion summary only if there were any results
        total_processed = (batch_results['successful_sends'] + batch_results['failed_sends'] +
                          batch_results['blocked_users'] + batch_results['deactivated_users'] +
                          batch_results['chat_not_found'])
        if total_processed > 0:
            logger.info(f"Batch completed: {batch_results['successful_sends']} sent, "
                       f"{batch_results['failed_sends']} failed, {batch_results['blocked_users']} blocked")

        return batch_results

    async def _send_media_message(self, user_id: int, media_data: Dict, caption: str = None, reply_markup=None):
        """Send media message with proper type handling"""
        media_type = media_data['type']
        file_id = media_data['file_id']

        if media_type == 'photo':
            await self.bot.send_photo(
                chat_id=user_id,
                photo=file_id,
                caption=caption if caption else None,
                reply_markup=reply_markup,
                parse_mode='HTML'
            )
        elif media_type == 'video':
            await self.bot.send_video(
                chat_id=user_id,
                video=file_id,
                caption=caption if caption else None,
                reply_markup=reply_markup,
                parse_mode='HTML'
            )
        elif media_type == 'document':
            await self.bot.send_document(
                chat_id=user_id,
                document=file_id,
                caption=caption if caption else None,
                reply_markup=reply_markup,
                parse_mode='HTML'
            )
        elif media_type == 'audio':
            await self.bot.send_audio(
                chat_id=user_id,
                audio=file_id,
                caption=caption if caption else None,
                reply_markup=reply_markup,
                parse_mode='HTML'
            )
        elif media_type == 'voice':
            await self.bot.send_voice(
                chat_id=user_id,
                voice=file_id,
                caption=caption if caption else None,
                reply_markup=reply_markup,
                parse_mode='HTML'
            )
        elif media_type == 'sticker':
            await self.bot.send_sticker(
                chat_id=user_id,
                sticker=file_id,
                reply_markup=reply_markup
            )
        elif media_type == 'animation':
            await self.bot.send_animation(
                chat_id=user_id,
                animation=file_id,
                caption=caption if caption else None,
                reply_markup=reply_markup,
                parse_mode='HTML'
            )
        elif media_type == 'video_note':
            await self.bot.send_video_note(
                chat_id=user_id,
                video_note=file_id,
                reply_markup=reply_markup
            )
        else:
            raise ValueError(f"Unsupported media type: {media_type}")

    def _create_inline_keyboard(self, buttons_data: List[List[Dict[str, str]]]):
        """Create inline keyboard from button data"""
        try:
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            keyboard_rows = []

            for button_row in buttons_data:
                keyboard_row = []
                for button in button_row:
                    text = button.get('text', '')
                    url = button.get('url', '')

                    if text and url:
                        keyboard_row.append(InlineKeyboardButton(text, url=url))

                if keyboard_row:
                    keyboard_rows.append(keyboard_row)

            if keyboard_rows:
                return InlineKeyboardMarkup(keyboard_rows)
            else:
                return None

        except Exception as e:
            logger.error(f"Error creating inline keyboard: {e}")
            return None

    async def _cleanup_inactive_users(self, cleaned_users: List[Dict]):
        """Mark inactive users as blocked/deactivated in database with optimized bulk operations"""
        try:
            if not cleaned_users:
                return

            users_collection = await get_collection(COLLECTIONS['users'])

            # Group users by reason for efficient bulk operations
            blocked_users = [u['user_id'] for u in cleaned_users if u['reason'] == 'blocked']
            deactivated_users = [u['user_id'] for u in cleaned_users if u['reason'] in ['deactivated', 'chat_not_found']]

            # Use bulk operations for better performance
            from pymongo import UpdateMany
            bulk_operations = []

            # Add blocked users to bulk operations
            if blocked_users:
                bulk_operations.append(
                    UpdateMany(
                        {"user_id": {"$in": blocked_users}},
                        {
                            "$set": {
                                "banned": True,
                                "ban_reason": "bot_blocked",
                                "banned_at": get_current_timestamp()
                            }
                        }
                    )
                )

            # Add deactivated users to bulk operations
            if deactivated_users:
                bulk_operations.append(
                    UpdateMany(
                        {"user_id": {"$in": deactivated_users}},
                        {
                            "$set": {
                                "banned": True,
                                "ban_reason": "account_deactivated",
                                "banned_at": get_current_timestamp()
                            }
                        }
                    )
                )

            # Execute all operations in a single bulk write
            if bulk_operations:
                result = await users_collection.bulk_write(bulk_operations, ordered=False)
                logger.info(f"Bulk cleanup: {result.modified_count} users updated")

        except Exception as e:
            logger.error(f"Error cleaning up inactive users: {e}")

    async def _mark_user_as_banned(self, user_id: int, reason: str):
        """Mark a single user as banned immediately for real-time cleanup"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])

            result = await users_collection.update_one(
                {"user_id": user_id},
                {
                    "$set": {
                        "banned": True,
                        "ban_reason": reason,
                        "banned_at": get_current_timestamp()
                    }
                }
            )

            if result.modified_count > 0:
                logger.info(f"Marked user {user_id} as banned (reason: {reason})")

        except Exception as e:
            logger.error(f"Error marking user {user_id} as banned: {e}")

    async def _update_broadcast_progress(self, broadcast_id: str, stats: Dict, processed_count: int):
        """Update broadcast progress in database"""
        try:
            collection = await get_collection(COLLECTIONS['broadcast_logs'])
            await collection.update_one(
                {"broadcast_id": broadcast_id},
                {
                    "$set": {
                        "successful_sends": stats["successful_sends"],
                        "failed_sends": stats["failed_sends"],
                        "blocked_users": stats["blocked_users"],
                        "deactivated_users": stats["deactivated_users"],
                        "chat_not_found": stats["chat_not_found"],
                        "rate_limited": stats["rate_limited"],
                        "other_errors": stats["other_errors"],
                        "processed_count": processed_count,
                        "last_updated": get_current_timestamp()
                    }
                }
            )
        except Exception as e:
            logger.error(f"Error updating broadcast progress: {e}")

    async def _finalize_broadcast(self, broadcast_id: str, stats: Dict):
        """Finalize broadcast with complete statistics"""
        try:
            collection = await get_collection(COLLECTIONS['broadcast_logs'])

            # Calculate success rate
            total_attempted = stats["successful_sends"] + stats["failed_sends"] + stats["blocked_users"] + stats["deactivated_users"] + stats["chat_not_found"] + stats["other_errors"]
            success_rate = round((stats["successful_sends"] / total_attempted) * 100, 2) if total_attempted > 0 else 0

            await collection.update_one(
                {"broadcast_id": broadcast_id},
                {
                    "$set": {
                        "status": "completed",
                        "successful_sends": stats["successful_sends"],
                        "failed_sends": stats["failed_sends"],
                        "blocked_users": stats["blocked_users"],
                        "deactivated_users": stats["deactivated_users"],
                        "chat_not_found": stats["chat_not_found"],
                        "rate_limited": stats["rate_limited"],
                        "other_errors": stats["other_errors"],
                        "cleaned_users_count": len(stats["cleaned_users"]),
                        "success_rate": success_rate,
                        "completed_at": get_current_timestamp()
                    }
                }
            )

            # Log broadcast completion
            from services.admin_logging_service import AdminLoggingService
            logging_service = AdminLoggingService()
            await logging_service.log_broadcast_completed(
                broadcast_id,
                broadcast_id,
                {
                    "total_users": stats["total_users"],
                    "successful_sends": stats["successful_sends"],
                    "failed_sends": stats["failed_sends"],
                    "blocked_users": stats["blocked_users"],
                    "deactivated_users": stats["deactivated_users"],
                    "cleaned_users": len(stats["cleaned_users"]),
                    "success_rate": success_rate
                }
            )

        except Exception as e:
            logger.error(f"Error finalizing broadcast: {e}")

    async def _process_users_concurrent_batches(
        self, users_collection, broadcast_id: str, broadcast_data: Dict, stats: Dict,
        batch_size: int, concurrent_batches: int, delay_between_batches: float,
        delay_between_messages: float, progress_update_interval: int, progress_callback, start_time
    ) -> int:
        """Process users with concurrent batch processing for maximum performance"""
        try:
            # Create semaphore to limit concurrent operations
            semaphore = asyncio.Semaphore(concurrent_batches)

            # Get users cursor with optimized query
            cursor = users_collection.find(
                {"banned": {"$ne": True}},
                {"user_id": 1, "first_name": 1, "username": 1, "_id": 0}
            ).batch_size(batch_size * concurrent_batches)

            # Collect batches for concurrent processing
            batches = []
            current_batch = []
            processed_count = 0

            async for user in cursor:
                current_batch.append(user)

                if len(current_batch) >= batch_size:
                    batches.append(current_batch.copy())
                    current_batch = []

                    # Process batches when we have enough for concurrent execution
                    if len(batches) >= concurrent_batches:
                        await self._execute_concurrent_batches(
                            batches, broadcast_data, stats, semaphore, broadcast_id, delay_between_messages
                        )

                        processed_count += sum(len(batch) for batch in batches)

                        # Update performance monitor with current stats
                        performance_monitor.update_progress(
                            processed_count, stats["successful_sends"],
                            stats["failed_sends"] + stats["blocked_users"] + stats["deactivated_users"] + stats["chat_not_found"] + stats["other_errors"]
                        )

                        # Update progress periodically
                        if processed_count % progress_update_interval == 0 or processed_count >= stats["total_users"]:
                            if progress_callback:
                                await progress_callback(stats, processed_count, stats["total_users"], start_time)
                            await self._update_broadcast_progress(broadcast_id, stats, processed_count)

                            # Log performance stats
                            perf_stats = performance_monitor.get_performance_stats()
                            logger.info(f"Broadcast progress: {perf_stats['progress_percentage']}% "
                                      f"({perf_stats['current_rate_per_second']} msg/sec, "
                                      f"ETA: {perf_stats['estimated_remaining_time_minutes']} min)")

                        batches = []
                        await asyncio.sleep(delay_between_batches)

            # Process remaining batches
            if current_batch:
                batches.append(current_batch)

            if batches:
                await self._execute_concurrent_batches(
                    batches, broadcast_data, stats, semaphore, broadcast_id, delay_between_messages
                )

                # Update processed count for remaining batches
                processed_count += sum(len(batch) for batch in batches)

                # Update performance monitor for remaining batches
                performance_monitor.update_progress(
                    processed_count, stats["successful_sends"],
                    stats["failed_sends"] + stats["blocked_users"] + stats["deactivated_users"] + stats["chat_not_found"] + stats["other_errors"]
                )

                # Final progress update for remaining batches
                if progress_callback:
                    await progress_callback(stats, processed_count, stats["total_users"], start_time)

            return processed_count

        except Exception as e:
            logger.error(f"Error in concurrent batch processing: {e}")
            return processed_count if 'processed_count' in locals() else 0

    def _extract_retry_after_seconds(self, error_str: str) -> int:
        """Extract retry_after seconds from Telegram error message"""
        try:
            import re
            # Look for "retry after X" pattern
            match = re.search(r'retry after (\d+)', error_str.lower())
            if match:
                return int(match.group(1))

            # Look for "Too Many Requests: retry after X" pattern
            match = re.search(r'too many requests.*?(\d+)', error_str.lower())
            if match:
                return int(match.group(1))

            # Default backoff
            return 5
        except Exception:
            return 5  # Default 5 second backoff

    async def _execute_concurrent_batches(
        self, batches: List[List[Dict]], broadcast_data: Dict, stats: Dict,
        semaphore: asyncio.Semaphore, broadcast_id: str, delay_between_messages: float
    ):
        """Execute multiple batches concurrently"""
        try:
            # Create tasks for concurrent execution
            tasks = []
            for batch in batches:
                task = self._process_batch_with_semaphore(
                    batch, broadcast_data, semaphore, broadcast_id, delay_between_messages
                )
                tasks.append(task)

            # Execute all batches concurrently
            batch_results_list = await asyncio.gather(*tasks, return_exceptions=True)

            # Aggregate results from all concurrent batches
            for batch_results in batch_results_list:
                if isinstance(batch_results, Exception):
                    logger.error(f"Batch failed with exception: {batch_results}")
                    continue

                if isinstance(batch_results, dict) and not batch_results.get("cancelled"):
                    for key, value in batch_results.items():
                        if key in stats:
                            if isinstance(stats[key], list):
                                if isinstance(value, list):
                                    stats[key].extend(value)
                            else:
                                stats[key] += value

        except Exception as e:
            logger.error(f"Error executing concurrent batches: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

    async def _process_batch_with_semaphore(
        self, batch: List[Dict], broadcast_data: Dict,
        semaphore: asyncio.Semaphore, broadcast_id: str, delay_between_messages: float
    ) -> Dict:
        """Process a single batch with semaphore control"""
        async with semaphore:
            # Check if broadcast was cancelled
            if await self._is_broadcast_cancelled(broadcast_id):
                return {"cancelled": True}

            # Process the batch and return results
            return await self._process_user_batch_optimized(batch, broadcast_data, {}, delay_between_messages)
