"""
Statistics service for bot analytics and reporting
Handles comprehensive data retrieval and analysis
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
import asyncio

from config.database import get_collection, COLLECTIONS
from utils.helpers import get_current_timestamp

logger = logging.getLogger(__name__)

class StatisticsService:
    """Service for bot statistics and analytics"""
    
    def __init__(self):
        self._cache = {}
        self._cache_ttl = 300  # 5 minutes cache
    
    async def _get_cached_or_fetch(self, key: str, fetch_func, *args, **kwargs):
        """Get cached data or fetch new data"""
        current_time = get_current_timestamp()
        
        if key in self._cache:
            data, timestamp = self._cache[key]
            if current_time - timestamp < self._cache_ttl:
                return data
        
        # Fetch new data
        data = await fetch_func(*args, **kwargs)
        self._cache[key] = (data, current_time)
        return data
    
    async def get_overall_statistics(self) -> Dict[str, Any]:
        """Get comprehensive bot statistics"""
        return await self._get_cached_or_fetch('overall_stats', self._fetch_overall_statistics)

    async def get_daily_statistics(self, date_str: str = None) -> Dict[str, Any]:
        """Get statistics for a specific date (YYYY-MM-DD format) or yesterday if None"""
        try:
            from datetime import datetime, timedelta

            if date_str:
                # Parse provided date
                try:
                    if '-' in date_str and len(date_str.split('-')[0]) == 4:
                        # YYYY-MM-DD format
                        target_date = datetime.strptime(date_str, '%Y-%m-%d')
                    elif '-' in date_str and len(date_str.split('-')[0]) <= 2:
                        # DD-MM-YYYY format
                        target_date = datetime.strptime(date_str, '%d-%m-%Y')
                    else:
                        return {'error': 'Invalid date format. Use YYYY-MM-DD or DD-MM-YYYY'}
                except ValueError:
                    return {'error': 'Invalid date format. Use YYYY-MM-DD or DD-MM-YYYY'}
            else:
                # Default to yesterday
                target_date = datetime.now() - timedelta(days=1)

            # Calculate start and end timestamps for the target date
            start_of_day = target_date.replace(hour=0, minute=0, second=0, microsecond=0)
            end_of_day = target_date.replace(hour=23, minute=59, second=59, microsecond=999999)

            start_timestamp = int(start_of_day.timestamp())
            end_timestamp = int(end_of_day.timestamp())

            # Get collections
            users_collection = await get_collection(COLLECTIONS['users'])
            withdrawals_collection = await get_collection(COLLECTIONS['withdrawals'])

            # Users joined on target date
            users_joined = await users_collection.count_documents({
                'created_at': {'$gte': start_timestamp, '$lt': end_timestamp}
            })

            # Referrals made on target date (count users who were referred and processed on this date)
            # Since promotion_report doesn't have timestamps, we count users who were referred on this date
            total_referrals = await users_collection.count_documents({
                'created_at': {'$gte': start_timestamp, '$lt': end_timestamp},
                'referred_by': {'$ne': 'None'},
                'referred': True
            })

            # Withdrawals created on target date (all statuses for activity tracking)
            withdrawals_pipeline = [
                {'$match': {
                    'created_at': {'$gte': start_timestamp, '$lt': end_timestamp}
                }},
                {'$group': {
                    '_id': None,
                    'total_amount': {'$sum': '$amount'},
                    'count': {'$sum': 1}
                }}
            ]

            withdrawal_result = await withdrawals_collection.aggregate(withdrawals_pipeline).to_list(length=1)
            withdrawal_data = withdrawal_result[0] if withdrawal_result else {'total_amount': 0, 'count': 0}

            # Joining bonuses distributed on target date
            bonus_pipeline = [
                {'$match': {
                    'created_at': {'$gte': start_timestamp, '$lt': end_timestamp},
                    'joining_bonus_got': {'$gt': 0}
                }},
                {'$group': {
                    '_id': None,
                    'total_bonus': {'$sum': '$joining_bonus_got'},
                    'count': {'$sum': 1}
                }}
            ]

            bonus_result = await users_collection.aggregate(bonus_pipeline).to_list(length=1)
            bonus_data = bonus_result[0] if bonus_result else {'total_bonus': 0, 'count': 0}

            return {
                'date': target_date.strftime('%Y-%m-%d'),
                'date_formatted': target_date.strftime('%d %B %Y'),
                'users_joined': users_joined,
                'total_referrals': total_referrals,
                'withdrawals_processed': withdrawal_data['count'],
                'total_withdrawal_amount': withdrawal_data['total_amount'],
                'joining_bonuses_given': bonus_data['count'],
                'total_joining_bonus_amount': bonus_data['total_bonus']
            }

        except Exception as e:
            logger.error(f"Error getting daily statistics: {e}")
            return {'error': f'Error retrieving statistics: {str(e)}'}
    
    async def _fetch_overall_statistics(self) -> Dict[str, Any]:
        """Fetch overall bot statistics from database"""
        try:
            stats = {}
            
            # User statistics
            users_collection = await get_collection(COLLECTIONS['users'])
            total_users = await users_collection.count_documents({})
            active_users = await users_collection.count_documents({"banned": {"$ne": True}})

            stats['total_users'] = total_users
            stats['active_users'] = active_users

            # Financial statistics
            pipeline = [
                {"$group": {
                    "_id": None,
                    "total_balance": {"$sum": "$balance"},
                    "total_successful_withdrawals": {"$sum": "$successful_withdraw"},
                    "total_pending_withdrawals": {"$sum": "$withdraw_under_review"},
                    "unique_banks": {"$addToSet": "$account_info.account_number"},
                    "unique_usdt": {"$addToSet": "$account_info.usdt_address"}
                }}
            ]
            
            financial_result = await users_collection.aggregate(pipeline).to_list(length=1)
            if financial_result:
                stats['total_user_balance'] = financial_result[0].get('total_balance', 0)
                stats['total_successful_withdrawals'] = financial_result[0].get('total_successful_withdrawals', 0)
                stats['total_pending_withdrawals'] = financial_result[0].get('total_pending_withdrawals', 0)
                stats['unique_banks_count'] = len([x for x in financial_result[0].get('unique_banks', []) if x])
                stats['unique_usdt_count'] = len([x for x in financial_result[0].get('unique_usdt', []) if x])
            else:
                stats['total_user_balance'] = 0
                stats['total_successful_withdrawals'] = 0
                stats['total_pending_withdrawals'] = 0
                stats['unique_banks_count'] = 0
                stats['unique_usdt_count'] = 0
            
            # Calculate withdrawal statistics from user withdrawal_reports
            # Note: withdrawal_reports.method is empty, so we infer from account_info
            withdrawal_stats_pipeline = [
                {"$unwind": {"path": "$withdrawal_reports", "preserveNullAndEmptyArrays": False}},
                {"$addFields": {
                    # Enhanced method inference: use withdrawal_method if available, otherwise infer from account details
                    "inferred_method": {
                        "$cond": [
                            {"$and": [
                                {"$ne": ["$account_info.withdrawal_method", ""]},
                                {"$ne": ["$account_info.withdrawal_method", None]}
                            ]},
                            "$account_info.withdrawal_method",  # Use original if available and not empty
                            {
                                "$cond": [
                                    {"$and": [
                                        {"$ne": ["$account_info.account_number", ""]},
                                        {"$ne": ["$account_info.account_number", None]}
                                    ]},
                                    "bank",  # Has account number = bank
                                    {
                                        "$cond": [
                                            {"$and": [
                                                {"$ne": ["$account_info.usdt_address", ""]},
                                                {"$ne": ["$account_info.usdt_address", None]}
                                            ]},
                                            "usdt",  # Has USDT address = USDT
                                            "bank"  # Default to bank if can't determine
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                }},
                {"$group": {
                    "_id": None,
                    "total_withdrawals": {"$sum": 1},
                    "passed_withdrawals": {
                        "$sum": {"$cond": [{"$eq": ["$withdrawal_reports.status", "Passed"]}, 1, 0]}
                    },
                    "failed_withdrawals": {
                        "$sum": {"$cond": [{"$eq": ["$withdrawal_reports.status", "Failed"]}, 1, 0]}
                    },
                    "pending_withdrawals": {
                        "$sum": {"$cond": [{"$and": [
                            {"$ne": ["$withdrawal_reports.status", "Passed"]},
                            {"$ne": ["$withdrawal_reports.status", "Failed"]}
                        ]}, 1, 0]}
                    },
                    "total_withdrawal_amount": {"$sum": "$withdrawal_reports.amount"},
                    "passed_withdrawal_amount": {
                        "$sum": {"$cond": [
                            {"$eq": ["$withdrawal_reports.status", "Passed"]},
                            "$withdrawal_reports.amount",
                            0
                        ]}
                    },
                    "pending_withdrawal_amount": {
                        "$sum": {"$cond": [
                            {"$and": [
                                {"$ne": ["$withdrawal_reports.status", "Passed"]},
                                {"$ne": ["$withdrawal_reports.status", "Failed"]}
                            ]},
                            "$withdrawal_reports.amount",
                            0
                        ]}
                    },
                    # Use inferred method from account_info instead of withdrawal_reports.method
                    "bank_withdrawals": {
                        "$sum": {"$cond": [{"$eq": ["$inferred_method", "bank"]}, 1, 0]}
                    },
                    "usdt_withdrawals": {
                        "$sum": {"$cond": [{"$eq": ["$inferred_method", "usdt"]}, 1, 0]}
                    },
                    "bank_passed_withdrawals": {
                        "$sum": {"$cond": [{"$and": [
                            {"$eq": ["$withdrawal_reports.status", "Passed"]},
                            {"$eq": ["$inferred_method", "bank"]}
                        ]}, 1, 0]}
                    },
                    "usdt_passed_withdrawals": {
                        "$sum": {"$cond": [{"$and": [
                            {"$eq": ["$withdrawal_reports.status", "Passed"]},
                            {"$eq": ["$inferred_method", "usdt"]}
                        ]}, 1, 0]}
                    },
                    "bank_passed_amount": {
                        "$sum": {"$cond": [{"$and": [
                            {"$eq": ["$withdrawal_reports.status", "Passed"]},
                            {"$eq": ["$inferred_method", "bank"]}
                        ]}, "$withdrawal_reports.amount", 0]}
                    },
                    "usdt_passed_amount": {
                        "$sum": {"$cond": [{"$and": [
                            {"$eq": ["$withdrawal_reports.status", "Passed"]},
                            {"$eq": ["$inferred_method", "usdt"]}
                        ]}, "$withdrawal_reports.amount", 0]}}
                }}
            ]

            withdrawal_stats_result = await users_collection.aggregate(withdrawal_stats_pipeline).to_list(length=1)

            if withdrawal_stats_result:
                withdrawal_data = withdrawal_stats_result[0]
                stats['completed_withdrawals'] = withdrawal_data.get('passed_withdrawals', 0)
                stats['total_withdrawn'] = withdrawal_data.get('passed_withdrawal_amount', 0)
                stats['pending_withdrawals'] = withdrawal_data.get('pending_withdrawals', 0)
                stats['pending_withdrawal_amount'] = withdrawal_data.get('pending_withdrawal_amount', 0)
                stats['bank_withdrawals_count'] = withdrawal_data.get('bank_withdrawals', 0)
                stats['usdt_withdrawals_count'] = withdrawal_data.get('usdt_withdrawals', 0)
                stats['bank_passed_withdrawals'] = withdrawal_data.get('bank_passed_withdrawals', 0)
                stats['usdt_passed_withdrawals'] = withdrawal_data.get('usdt_passed_withdrawals', 0)
                stats['bank_passed_amount'] = withdrawal_data.get('bank_passed_amount', 0)
                stats['usdt_passed_amount'] = withdrawal_data.get('usdt_passed_amount', 0)
                stats['total_withdrawal_requests'] = withdrawal_data.get('total_withdrawals', 0)
            else:
                stats['completed_withdrawals'] = 0
                stats['total_withdrawn'] = 0
                stats['pending_withdrawals'] = 0
                stats['pending_withdrawal_amount'] = 0
                stats['bank_withdrawals_count'] = 0
                stats['usdt_withdrawals_count'] = 0
                stats['bank_passed_withdrawals'] = 0
                stats['usdt_passed_withdrawals'] = 0
                stats['bank_passed_amount'] = 0
                stats['usdt_passed_amount'] = 0
                stats['total_withdrawal_requests'] = 0

            # Calculate total referral earnings across all users
            referral_earnings_pipeline = [
                {"$unwind": {"path": "$promotion_report", "preserveNullAndEmptyArrays": False}},
                {"$group": {
                    "_id": None,
                    "total_referral_earnings": {"$sum": "$promotion_report.amount_got"},
                    "total_referrals_made": {"$sum": 1}
                }}
            ]

            referral_result = await users_collection.aggregate(referral_earnings_pipeline).to_list(length=1)

            if referral_result:
                stats['total_referral_earnings'] = referral_result[0].get('total_referral_earnings', 0)
                stats['total_referrals_made'] = referral_result[0].get('total_referrals_made', 0)
            else:
                stats['total_referral_earnings'] = 0
                stats['total_referrals_made'] = 0


            
            # Gift code statistics
            try:
                gift_codes_collection = await get_collection(COLLECTIONS['gift_codes'])
                claimed_codes = await gift_codes_collection.count_documents({"is_used": True})

                gift_amount_pipeline = [
                    {"$match": {"is_used": True}},
                    {"$group": {"_id": None, "total_gift_value": {"$sum": "$amount"}}}
                ]
                gift_result = await gift_codes_collection.aggregate(gift_amount_pipeline).to_list(length=1)
                gift_amount = gift_result[0].get('total_gift_value', 0) if gift_result else 0

                stats['claimed_codes_count'] = claimed_codes
                stats['total_gift_value'] = gift_amount
            except Exception as e:
                logger.warning(f"Error getting gift code statistics: {e}")
                stats['claimed_codes_count'] = 0
                stats['total_gift_value'] = 0

            # Task submission statistics
            try:
                submissions_collection = await get_collection(COLLECTIONS['task_submissions'])
                total_submissions = await submissions_collection.count_documents({})
                stats['total_task_submissions'] = total_submissions
            except Exception as e:
                logger.warning(f"Error getting task submission statistics: {e}")
                stats['total_task_submissions'] = 0

            # Referral statistics
            try:
                referral_pipeline = [
                    {"$match": {"promotion_report": {"$exists": True, "$ne": []}}},
                    {"$project": {
                        "referral_count": {"$size": "$promotion_report"},
                        "referral_earnings": {"$sum": "$promotion_report.amount_got"}
                    }},
                    {"$group": {
                        "_id": None,
                        "total_referrals": {"$sum": "$referral_count"},
                        "total_referral_earnings": {"$sum": "$referral_earnings"},
                        "users_with_referrals": {"$sum": 1}
                    }}
                ]

                referral_result = await users_collection.aggregate(referral_pipeline).to_list(length=1)
                if referral_result:
                    stats['total_referrals'] = referral_result[0].get('total_referrals', 0)
                    stats['total_referral_earnings'] = referral_result[0].get('total_referral_earnings', 0)
                    stats['users_with_referrals'] = referral_result[0].get('users_with_referrals', 0)
                else:
                    stats['total_referrals'] = 0
                    stats['total_referral_earnings'] = 0
                    stats['users_with_referrals'] = 0
            except Exception as e:
                logger.warning(f"Error getting referral statistics: {e}")
                stats['total_referrals'] = 0
                stats['total_referral_earnings'] = 0
                stats['users_with_referrals'] = 0

            return stats
            
        except Exception as e:
            logger.error(f"Error fetching overall statistics: {e}")
            # Return basic fallback statistics instead of empty dict
            return {
                'total_users': 0,
                'active_users': 0,
                'total_user_balance': 0,
                'unique_banks_count': 0,
                'unique_usdt_count': 0,
                'completed_withdrawals': 0,
                'bank_withdrawals_count': 0,
                'usdt_withdrawals_count': 0,
                'bank_total_with_tax': 0,
                'bank_total_without_tax': 0,
                'usdt_total_with_tax': 0,
                'usdt_total_without_tax': 0,
                'total_withdrawn_with_tax': 0,
                'total_withdrawn_without_tax': 0,
                'pending_bank_withdrawals': 0,
                'pending_usdt_withdrawals': 0,
                'pending_withdrawals_count': 0,
                'pending_bank_amount': 0,
                'pending_usdt_amount': 0,
                'pending_withdrawals_amount': 0,
                'claimed_codes_count': 0,
                'total_gift_value': 0,
                'total_task_submissions': 0,
                'total_referrals': 0,
                'total_referral_earnings': 0,
                'users_with_referrals': 0
            }
    
    async def get_top_balances(self, limit: int = 30) -> List[Dict[str, Any]]:
        """Get top users by balance"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])
            
            pipeline = [
                {"$match": {"balance": {"$gt": 0}}},
                {"$sort": {"balance": -1}},
                {"$limit": limit},
                {"$project": {
                    "user_id": 1,
                    "username": 1,
                    "first_name": 1,
                    "balance": 1
                }}
            ]
            
            result = await users_collection.aggregate(pipeline).to_list(length=limit)
            return result
            
        except Exception as e:
            logger.error(f"Error getting top balances: {e}")
            return []
    
    async def get_top_inviters(self, limit: int = 30) -> List[Dict[str, Any]]:
        """Get top users by referral count"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])

            pipeline = [
                {"$addFields": {
                    "total_referrals": {"$size": {"$ifNull": ["$promotion_report", []]}},
                    "referral_earnings": {"$sum": {"$ifNull": ["$promotion_report.amount_got", []]}}
                }},
                {"$match": {"total_referrals": {"$gt": 0}}},
                {"$sort": {"total_referrals": -1}},
                {"$limit": limit},
                {"$project": {
                    "user_id": 1,
                    "username": 1,
                    "first_name": 1,
                    "total_referrals": 1,
                    "referral_earnings": 1
                }}
            ]

            result = await users_collection.aggregate(pipeline).to_list(length=limit)
            return result

        except Exception as e:
            logger.error(f"Error getting top inviters: {e}")
            return []
    
    async def get_top_withdrawers(self, limit: int = 30) -> List[Dict[str, Any]]:
        """Get top users by total withdrawal amount"""
        try:
            # First try to get from withdrawals collection
            withdrawals_collection = await get_collection(COLLECTIONS['withdrawals'])

            # Try multiple status values that might be used
            pipeline = [
                {"$match": {"status": {"$in": ["Passed", "completed", "approved", "success"]}}},
                {"$group": {
                    "_id": "$user_id",
                    "total_withdrawn": {"$sum": "$amount"},
                    "withdrawal_count": {"$sum": 1}
                }},
                {"$sort": {"total_withdrawn": -1}},
                {"$limit": limit}
            ]

            withdrawal_data = await withdrawals_collection.aggregate(pipeline).to_list(length=limit)

            # If no data from withdrawals collection, try using user's successful_withdraw field
            if not withdrawal_data:
                users_collection = await get_collection(COLLECTIONS['users'])
                pipeline = [
                    {"$match": {"successful_withdraw": {"$gt": 0}}},
                    {"$sort": {"successful_withdraw": -1}},
                    {"$limit": limit},
                    {"$project": {
                        "user_id": 1,
                        "username": 1,
                        "first_name": 1,
                        "successful_withdraw": 1
                    }}
                ]

                user_data = await users_collection.aggregate(pipeline).to_list(length=limit)

                result = []
                for user in user_data:
                    result.append({
                        "user_id": user["user_id"],
                        "username": user.get("username"),
                        "first_name": user.get("first_name"),
                        "total_withdrawn": user["successful_withdraw"],
                        "withdrawal_count": 1  # We don't have count data from user field
                    })

                return result

            # Get user details for withdrawal collection data
            users_collection = await get_collection(COLLECTIONS['users'])
            result = []

            for item in withdrawal_data:
                user = await users_collection.find_one({"user_id": item["_id"]})
                if user:
                    result.append({
                        "user_id": item["_id"],
                        "username": user.get("username"),
                        "first_name": user.get("first_name"),
                        "total_withdrawn": item["total_withdrawn"],
                        "withdrawal_count": item["withdrawal_count"]
                    })

            return result

        except Exception as e:
            logger.error(f"Error getting top withdrawers: {e}")
            return []
    
    async def get_pending_withdrawals(self) -> List[Dict[str, Any]]:
        """Get all pending withdrawal requests"""
        try:
            withdrawals_collection = await get_collection(COLLECTIONS['withdrawals'])
            
            pipeline = [
                {"$match": {"status": "Under review"}},
                {"$sort": {"created_at": -1}},
                {"$lookup": {
                    "from": "users",
                    "localField": "user_id",
                    "foreignField": "user_id",
                    "as": "user_info"
                }},
                {"$unwind": "$user_info"},
                {"$project": {
                    "user_id": 1,
                    "amount": 1,
                    "withdrawal_method": 1,
                    "created_at": 1,
                    "username": "$user_info.username",
                    "first_name": "$user_info.first_name"
                }}
            ]
            
            result = await withdrawals_collection.aggregate(pipeline).to_list(length=None)
            return result
            
        except Exception as e:
            logger.error(f"Error getting pending withdrawals: {e}")
            return []

    async def get_channel_statistics(self) -> List[Dict[str, Any]]:
        """Get channel-wise join statistics"""
        try:
            # Get force subscribe channels
            channels_collection = await get_collection(COLLECTIONS['force_channels'])
            channels = await channels_collection.find({}).to_list(length=None)

            if not channels:
                # If no force channels configured, return basic bot statistics
                users_collection = await get_collection(COLLECTIONS['users'])
                total_users = await users_collection.count_documents({})

                # Calculate time-based statistics
                from datetime import datetime, timedelta
                now = datetime.now()
                today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
                yesterday_start = today_start - timedelta(days=1)
                week_start = today_start - timedelta(days=7)
                month_start = today_start - timedelta(days=30)

                today_timestamp = int(today_start.timestamp())
                yesterday_timestamp = int(yesterday_start.timestamp())
                week_timestamp = int(week_start.timestamp())
                month_timestamp = int(month_start.timestamp())

                joined_today = await users_collection.count_documents({"created_at": {"$gte": today_timestamp}})
                joined_yesterday = await users_collection.count_documents({
                    "created_at": {"$gte": yesterday_timestamp, "$lt": today_timestamp}
                })
                joined_this_week = await users_collection.count_documents({"created_at": {"$gte": week_timestamp}})
                joined_this_month = await users_collection.count_documents({"created_at": {"$gte": month_timestamp}})

                return [{
                    "channel_id": "bot_general",
                    "channel_name": "Bot General Statistics",
                    "total_members": total_users,
                    "joined_today": joined_today,
                    "joined_yesterday": joined_yesterday,
                    "joined_this_week": joined_this_week,
                    "joined_this_month": joined_this_month
                }]

            result = []
            for channel in channels:
                # For configured channels, provide basic statistics
                # In a real implementation, you would track channel-specific joins
                users_collection = await get_collection(COLLECTIONS['users'])
                total_users = await users_collection.count_documents({})

                channel_stats = {
                    "channel_id": channel.get("channel_id"),
                    "channel_name": channel.get("channel_name", "Unknown Channel"),
                    "total_members": total_users,  # All bot users (approximation)
                    "joined_today": 0,  # Would need channel-specific tracking
                    "joined_yesterday": 0,
                    "joined_this_week": 0,
                    "joined_this_month": 0
                }
                result.append(channel_stats)

            return result

        except Exception as e:
            logger.error(f"Error getting channel statistics: {e}")
            return []

    async def get_user_history(self, history_type: str, page: int = 1, limit: int = 15) -> Tuple[List[Dict[str, Any]], int]:
        """Get paginated user history by type"""
        try:
            skip = (page - 1) * limit

            if history_type == "recently_joined":
                return await self._get_recently_joined(skip, limit)
            elif history_type == "referral_rewards":
                return await self._get_referral_rewards(skip, limit)
            elif history_type == "claimed_gifts":
                return await self._get_claimed_gifts(skip, limit)
            elif history_type == "withdrawal_history":
                return await self._get_withdrawal_history(skip, limit)
            elif history_type == "custom_link_usage":
                return await self._get_custom_link_usage(skip, limit)
            elif history_type == "full_activity_log":
                return await self._get_full_activity_log(skip, limit)
            elif history_type == "task_completions":
                return await self._get_task_completions(skip, limit)
            else:
                return [], 0

        except Exception as e:
            logger.error(f"Error getting user history for {history_type}: {e}")
            return [], 0

    async def _get_recently_joined(self, skip: int, limit: int) -> Tuple[List[Dict[str, Any]], int]:
        """Get recently joined users"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])

            total = await users_collection.count_documents({})

            pipeline = [
                {"$sort": {"created_at": -1}},
                {"$skip": skip},
                {"$limit": limit},
                {"$project": {
                    "user_id": 1,
                    "username": 1,
                    "first_name": 1,
                    "created_at": 1,
                    "joining_bonus_got": 1,  # Use correct field name
                    "referred_by": 1
                }}
            ]

            users = await users_collection.aggregate(pipeline).to_list(length=limit)
            return users, total

        except Exception as e:
            logger.error(f"Error getting recently joined users: {e}")
            return [], 0

    async def _get_referral_rewards(self, skip: int, limit: int) -> Tuple[List[Dict[str, Any]], int]:
        """Get recent referral rewards"""
        try:
            users_collection = await get_collection(COLLECTIONS['users'])

            # Use promotion_report to calculate referral earnings
            pipeline = [
                {"$addFields": {
                    "referral_earnings": {"$sum": "$promotion_report.amount_got"},
                    "referral_count": {"$size": {"$ifNull": ["$promotion_report", []]}}
                }},
                {"$match": {"referral_earnings": {"$gt": 0}}},
                {"$sort": {"updated_at": -1}},  # Sort by last update instead of non-existent field
                {"$skip": skip},
                {"$limit": limit},
                {"$project": {
                    "user_id": 1,
                    "username": 1,
                    "first_name": 1,
                    "referral_earnings": 1,
                    "referral_count": 1,
                    "updated_at": 1
                }}
            ]

            # Count total users with referral earnings
            count_pipeline = [
                {"$addFields": {
                    "referral_earnings": {"$sum": "$promotion_report.amount_got"}
                }},
                {"$match": {"referral_earnings": {"$gt": 0}}},
                {"$count": "total"}
            ]

            count_result = await users_collection.aggregate(count_pipeline).to_list(length=1)
            total = count_result[0]["total"] if count_result else 0

            rewards = await users_collection.aggregate(pipeline).to_list(length=limit)

            return rewards, total

        except Exception as e:
            logger.error(f"Error getting referral rewards: {e}")
            return [], 0

    async def _get_claimed_gifts(self, skip: int, limit: int) -> Tuple[List[Dict[str, Any]], int]:
        """Get recent gift code claims"""
        try:
            gift_codes_collection = await get_collection(COLLECTIONS['gift_codes'])

            pipeline = [
                {"$match": {"is_used": True}},
                {"$sort": {"used_at": -1}},
                {"$skip": skip},
                {"$limit": limit},
                {"$lookup": {
                    "from": "users",
                    "localField": "used_by",
                    "foreignField": "user_id",
                    "as": "user_info"
                }},
                {"$unwind": "$user_info"},
                {"$project": {
                    "user_id": "$used_by",
                    "username": "$user_info.username",
                    "first_name": "$user_info.first_name",
                    "amount": 1,
                    "code": 1,
                    "used_at": 1
                }}
            ]

            total = await gift_codes_collection.count_documents({"is_used": True})
            gifts = await gift_codes_collection.aggregate(pipeline).to_list(length=limit)

            return gifts, total

        except Exception as e:
            logger.error(f"Error getting claimed gifts: {e}")
            return [], 0

    async def _get_withdrawal_history(self, skip: int, limit: int) -> Tuple[List[Dict[str, Any]], int]:
        """Get recent withdrawal history"""
        try:
            withdrawals_collection = await get_collection(COLLECTIONS['withdrawals'])

            # Try multiple status values that might be used
            status_match = {"status": {"$in": ["Passed", "completed", "approved", "success"]}}

            pipeline = [
                {"$match": status_match},
                {"$sort": {"updated_at": -1}},  # Use updated_at instead of completed_at
                {"$skip": skip},
                {"$limit": limit},
                {"$lookup": {
                    "from": "users",
                    "localField": "user_id",
                    "foreignField": "user_id",
                    "as": "user_info"
                }},
                {"$unwind": "$user_info"},
                {"$project": {
                    "user_id": 1,
                    "username": "$user_info.username",
                    "first_name": "$user_info.first_name",
                    "amount": 1,
                    "withdrawal_method": 1,
                    "updated_at": 1,
                    "status": 1
                }}
            ]

            total = await withdrawals_collection.count_documents(status_match)
            withdrawals = await withdrawals_collection.aggregate(pipeline).to_list(length=limit)

            return withdrawals, total

        except Exception as e:
            logger.error(f"Error getting withdrawal history: {e}")
            return [], 0

    async def _get_custom_link_usage(self, skip: int, limit: int) -> Tuple[List[Dict[str, Any]], int]:
        """Get custom referral link usage with correct field mapping"""
        try:
            custom_refs_collection = await get_collection(COLLECTIONS['custom_referrals'])

            # Calculate total rewards based on referrals (assuming 20 per referral as per system)
            pipeline = [
                {
                    "$addFields": {
                        "total_rewards": {"$multiply": ["$referrals", 20]},  # 20 rupees per referral
                        "usage_count": "$clicks",  # Map clicks to usage_count for display
                        "link_id": "$custom_param",  # Map custom_param to link_id for display
                        "last_used": "$last_clicked"  # Map last_clicked to last_used for display
                    }
                },
                {"$sort": {"last_clicked": -1}},  # Sort by actual last_clicked field
                {"$skip": skip},
                {"$limit": limit},
                {"$project": {
                    "link_id": 1,  # Now contains custom_param
                    "usage_count": 1,  # Now contains clicks
                    "total_rewards": 1,  # Now calculated from referrals
                    "last_used": 1,  # Now contains last_clicked
                    "created_by": 1,
                    "user_name": 1,
                    "user_id": 1,
                    "referrals": 1,
                    "clicks": 1,
                    "custom_param": 1,
                    "last_clicked": 1
                }}
            ]

            total = await custom_refs_collection.count_documents({})
            links = await custom_refs_collection.aggregate(pipeline).to_list(length=limit)

            return links, total

        except Exception as e:
            logger.error(f"Error getting custom link usage: {e}")
            return [], 0

    async def _get_full_activity_log(self, skip: int, limit: int) -> Tuple[List[Dict[str, Any]], int]:
        """Get full user activity log"""
        try:
            # This would need an activity_logs collection
            # For now, combining recent activities from different sources
            activities = []

            # Get recent joins
            users_collection = await get_collection(COLLECTIONS['users'])
            recent_joins = await users_collection.find({}).sort("created_at", -1).limit(5).to_list(length=5)

            for user in recent_joins:
                activities.append({
                    "user_id": user["user_id"],
                    "username": user.get("username"),
                    "first_name": user.get("first_name"),
                    "action": "joined",
                    "timestamp": user["created_at"],
                    "details": f"Joined the bot"
                })

            # Sort by timestamp and paginate
            activities.sort(key=lambda x: x["timestamp"], reverse=True)
            total = len(activities)

            start = skip
            end = skip + limit
            paginated_activities = activities[start:end]

            return paginated_activities, total

        except Exception as e:
            logger.error(f"Error getting full activity log: {e}")
            return [], 0

    async def _get_task_completions(self, skip: int, limit: int) -> Tuple[List[Dict[str, Any]], int]:
        """Get recent task completions"""
        try:
            submissions_collection = await get_collection(COLLECTIONS['task_submissions'])

            pipeline = [
                {"$match": {"status": "approved"}},
                {"$sort": {"approved_at": -1}},
                {"$skip": skip},
                {"$limit": limit},
                {"$lookup": {
                    "from": "users",
                    "localField": "user_id",
                    "foreignField": "user_id",
                    "as": "user_info"
                }},
                {"$unwind": "$user_info"},
                {"$lookup": {
                    "from": "tasks",
                    "localField": "task_id",
                    "foreignField": "task_id",
                    "as": "task_info"
                }},
                {"$unwind": "$task_info"},
                {"$project": {
                    "user_id": 1,
                    "username": "$user_info.username",
                    "first_name": "$user_info.first_name",
                    "task_name": "$task_info.name",
                    "reward_earned": "$task_info.reward_amount",
                    "approved_at": 1
                }}
            ]

            total = await submissions_collection.count_documents({"status": "approved"})
            completions = await submissions_collection.aggregate(pipeline).to_list(length=limit)

            return completions, total

        except Exception as e:
            logger.error(f"Error getting task completions: {e}")
            return [], 0

    def clear_cache(self):
        """Clear statistics cache"""
        self._cache.clear()
