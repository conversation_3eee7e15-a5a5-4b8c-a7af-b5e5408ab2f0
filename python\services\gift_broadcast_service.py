"""
Gift Broadcast service for channel-based reward campaigns
Handles channel validation, membership verification, and reward distribution
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
from telegram import Bo<PERSON>
from telegram.error import TelegramError

from config.database import get_collection, COLLECTIONS
from utils.helpers import get_current_timestamp, generate_unique_id
from config.settings import settings

logger = logging.getLogger(__name__)

class GiftBroadcastService:
    """Service for gift broadcast campaigns with channel join verification"""

    def __init__(self, bot: Bot = None):
        if bot:
            self.bot = bot
            self._bot_initialized = True
        else:
            # Create bot instance if not provided (for backward compatibility)
            self.bot = Bot(token=settings.BOT_TOKEN)
            self._bot_initialized = False

    async def _ensure_bot_initialized(self):
        """Ensure bot instance is properly initialized"""
        if not self._bot_initialized:
            try:
                # Initialize the bot by calling get_me
                await self.bot.initialize()
                self._bot_initialized = True
            except Exception as e:
                logger.error(f"Failed to initialize bot: {e}")
                raise RuntimeError("Bot initialization failed")
    
    async def validate_channel_input(self, channel_input: str) -> Dict[str, Any]:
        """Validate and parse channel input (username or ID)"""
        try:
            channel_input = channel_input.strip()
            
            # Check if it's a channel ID (starts with -100)
            if channel_input.startswith('-100') and channel_input[4:].isdigit():
                # Private channel ID
                return {
                    'type': 'private',
                    'channel_id': channel_input,
                    'needs_invite_link': True,
                    'valid': True
                }
            
            # Check if it's a public channel username
            elif channel_input.replace('@', '').isalnum():
                # Remove @ if present and validate
                username = channel_input.replace('@', '')
                channel_id = f"@{username}"
                
                return {
                    'type': 'public',
                    'channel_id': channel_id,
                    'username': username,
                    'needs_invite_link': False,
                    'valid': True
                }
            
            else:
                return {
                    'valid': False,
                    'error': 'Invalid format. Use channel username (without @) or channel ID (-100xxxxxxxxx)'
                }
                
        except Exception as e:
            logger.error(f"Error validating channel input: {e}")
            return {
                'valid': False,
                'error': 'Failed to validate channel input'
            }
    
    async def verify_channel_access(self, channel_id: str) -> Tuple[bool, str, Optional[Dict]]:
        """Verify bot has admin access to channel and get channel info"""
        try:
            # Ensure bot is initialized
            await self._ensure_bot_initialized()

            # Get channel information
            chat = await self.bot.get_chat(channel_id)
            
            # Check if bot is admin
            chat_member = await self.bot.get_chat_member(channel_id, self.bot.id)
            
            if chat_member.status not in ['administrator', 'creator']:
                return False, "Bot must be an administrator in the channel", None
            
            # Return channel info
            channel_info = {
                'id': str(chat.id),
                'title': chat.title,
                'username': chat.username,
                'type': chat.type,
                'member_count': getattr(chat, 'member_count', 0)
            }
            
            return True, "Channel access verified", channel_info
            
        except TelegramError as e:
            if "chat not found" in str(e).lower():
                return False, "Channel not found. Please check the channel ID/username", None
            elif "forbidden" in str(e).lower():
                return False, "Bot doesn't have access to this channel", None
            else:
                return False, f"Telegram error: {str(e)}", None
        except Exception as e:
            logger.error(f"Error verifying channel access: {e}")
            return False, "Failed to verify channel access", None
    
    async def create_gift_broadcast(
        self,
        channel_info: Dict[str, Any],
        invite_link: Optional[str],
        reward_amount: float,
        admin_user_id: int
    ) -> Tuple[bool, str, Optional[str]]:
        """Create a gift broadcast campaign"""
        try:
            # Generate unique broadcast ID
            broadcast_id = generate_unique_id("gift_broadcast")

            # Create gift broadcast record
            gift_broadcasts_collection = await get_collection(COLLECTIONS['gift_broadcasts'])

            gift_broadcast_data = {
                'broadcast_id': broadcast_id,
                'channel_id': channel_info['id'],
                'channel_title': channel_info['title'],
                'channel_username': channel_info.get('username'),
                'channel_type': channel_info['type'],
                'invite_link': invite_link,
                'reward_amount': reward_amount,
                'created_by': admin_user_id,
                'created_at': get_current_timestamp(),
                'status': 'active',
                'total_participants': 0,
                'total_rewards_distributed': 0,
                'participants': []
            }

            result = await gift_broadcasts_collection.insert_one(gift_broadcast_data)

            if result.inserted_id:
                return True, "Gift broadcast created successfully", broadcast_id
            else:
                return False, "Failed to create gift broadcast", None

        except Exception as e:
            logger.error(f"Error creating gift broadcast: {e}")
            return False, "Failed to create gift broadcast", None
    
    async def get_gift_broadcast(self, broadcast_id: str) -> Optional[Dict[str, Any]]:
        """Get gift broadcast by ID"""
        try:
            gift_broadcasts_collection = await get_collection(COLLECTIONS['gift_broadcasts'])
            return await gift_broadcasts_collection.find_one({'broadcast_id': broadcast_id})
        except Exception as e:
            logger.error(f"Error getting gift broadcast: {e}")
            return None
    
    async def check_user_participation(self, broadcast_id: str, user_id: int) -> bool:
        """Check if user has already participated in gift broadcast"""
        try:
            gift_broadcasts_collection = await get_collection(COLLECTIONS['gift_broadcasts'])
            broadcast = await gift_broadcasts_collection.find_one({
                'broadcast_id': broadcast_id,
                'participants': user_id
            })
            return broadcast is not None
        except Exception as e:
            logger.error(f"Error checking user participation: {e}")
            return False
    
    async def verify_channel_membership(self, channel_id: str, user_id: int) -> bool:
        """Verify if user is a member of the channel"""
        try:
            # Ensure bot is initialized
            await self._ensure_bot_initialized()

            chat_member = await self.bot.get_chat_member(channel_id, user_id)
            return chat_member.status in ['member', 'administrator', 'creator']
        except TelegramError:
            return False
        except Exception as e:
            logger.error(f"Error verifying channel membership: {e}")
            return False
    
    async def process_user_participation(self, broadcast_id: str, user_id: int) -> Tuple[bool, str, float, dict]:
        """Process user participation and distribute reward"""
        try:
            # Get gift broadcast
            gift_broadcast = await self.get_gift_broadcast(broadcast_id)
            if not gift_broadcast:
                return False, "Gift broadcast not found", 0, {}

            if gift_broadcast['status'] != 'active':
                return False, "Gift broadcast is no longer active", 0, {}

            # Check if user already participated
            if await self.check_user_participation(broadcast_id, user_id):
                return False, "You have already participated in this gift broadcast", 0, {}

            # Verify channel membership
            channel_id = gift_broadcast['channel_id']
            if not await self.verify_channel_membership(channel_id, user_id):
                # Return channel info for error message
                channel_info = {
                    'channel_title': gift_broadcast.get('channel_title', 'Channel'),
                    'invite_link': gift_broadcast.get('invite_link'),
                    'channel_username': gift_broadcast.get('channel_username'),
                    'broadcast_id': broadcast_id
                }
                return False, "You must join the channel first", 0, channel_info
            
            # Add user to participants and update statistics
            gift_broadcasts_collection = await get_collection(COLLECTIONS['gift_broadcasts'])
            reward_amount = gift_broadcast['reward_amount']
            
            await gift_broadcasts_collection.update_one(
                {'broadcast_id': broadcast_id},
                {
                    '$push': {'participants': user_id},
                    '$inc': {
                        'total_participants': 1,
                        'total_rewards_distributed': reward_amount
                    }
                }
            )
            
            # Credit user balance
            from services.user_service import UserService
            user_service = UserService()
            await user_service.update_balance(user_id, reward_amount, 'add')

            return True, f"Congratulations! You received ₹{reward_amount} for joining the channel!", reward_amount, {}

        except Exception as e:
            logger.error(f"Error processing user participation: {e}")
            return False, "Something went wrong while processing your participation", 0, {}
    
    async def get_broadcast_statistics(self, broadcast_id: str) -> Dict[str, Any]:
        """Get statistics for a gift broadcast"""
        try:
            gift_broadcast = await self.get_gift_broadcast(broadcast_id)
            if not gift_broadcast:
                return {}
            
            return {
                'broadcast_id': broadcast_id,
                'channel_title': gift_broadcast['channel_title'],
                'reward_amount': gift_broadcast['reward_amount'],
                'total_participants': gift_broadcast['total_participants'],
                'total_rewards_distributed': gift_broadcast['total_rewards_distributed'],
                'status': gift_broadcast['status'],
                'created_at': gift_broadcast['created_at']
            }
            
        except Exception as e:
            logger.error(f"Error getting broadcast statistics: {e}")
            return {}
    
    async def generate_join_message_and_keyboard(self, broadcast_id: str) -> Tuple[str, Any]:
        """Generate join message and keyboard for gift broadcast"""
        try:
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup
            
            gift_broadcast = await self.get_gift_broadcast(broadcast_id)
            if not gift_broadcast:
                return "Gift broadcast not found", None
            
            channel_title = gift_broadcast['channel_title']
            reward_amount = gift_broadcast['reward_amount']
            invite_link = gift_broadcast.get('invite_link')
            channel_username = gift_broadcast.get('channel_username')
            
            # Build message
            message = f"🎁 <b>Gift Broadcast!</b>\n\n"
            message += f"Join <b>{channel_title}</b> and get ₹{reward_amount}!\n\n"
            message += f"💰 Reward: ₹{reward_amount}\n"
            message += f"📢 Channel: {channel_title}\n\n"
            message += f"Click the button below to join and claim your reward!"
            
            # Build keyboard
            keyboard = []
            
            # Join button
            if invite_link:
                keyboard.append([InlineKeyboardButton(f'📢 Join {channel_title}', url=invite_link)])
            elif channel_username:
                keyboard.append([InlineKeyboardButton(f'📢 Join {channel_title}', url=f'https://t.me/{channel_username}')])
            
            # Verify button
            keyboard.append([InlineKeyboardButton('✅ Verify & Claim Reward', callback_data=f'verify_gift_broadcast_{broadcast_id}')])
            
            return message, InlineKeyboardMarkup(keyboard)
            
        except Exception as e:
            logger.error(f"Error generating join message: {e}")
            return "Error generating message", None

    async def start_gift_broadcast_to_all_users(self, broadcast_data: Dict[str, Any]) -> Tuple[bool, str]:
        """Start gift broadcast to all bot users with optimized progress tracking"""
        try:
            # Get active users count efficiently (don't load all into memory)
            from config.database import get_collection, COLLECTIONS
            users_collection = await get_collection(COLLECTIONS['users'])

            # Count active users first
            active_user_count = await users_collection.count_documents({"banned": {"$ne": True}})

            if active_user_count == 0:
                return False, "No active users found to broadcast to"

            # Use user IDs for target list (memory efficient)
            cursor = users_collection.find(
                {"banned": {"$ne": True}},
                {"user_id": 1, "_id": 0}
            )
            target_users = [user['user_id'] async for user in cursor]

            # Generate broadcast message and keyboard
            message = self._generate_gift_broadcast_message(broadcast_data)
            keyboard = self._generate_gift_broadcast_keyboard(broadcast_data)

            # Create message data for optimized broadcast
            message_data = {
                "type": "gift",
                "text": message,
                "reply_markup": keyboard.to_dict() if keyboard else None,
                "gift_data": broadcast_data
            }

            # Create broadcast session using the optimized system
            from models.broadcast import BroadcastModel
            broadcast_session = BroadcastModel.create_broadcast_session(
                admin_id=broadcast_data.get('admin_id', 0),
                broadcast_type="gift",
                message_data=message_data,
                target_users=target_users
            )

            # Save broadcast session
            from services.admin_service import AdminService
            admin_service = AdminService()
            broadcast_id = await admin_service.save_broadcast_session(broadcast_session)

            if not broadcast_id:
                return False, "Failed to create broadcast session"

            # Start optimized broadcast in background
            import asyncio
            asyncio.create_task(self._execute_gift_broadcast_optimized(broadcast_id, broadcast_data.get('admin_id', 0)))

            return True, f"🎁 Gift broadcast started! ID: {broadcast_id[-8:]}\n\nBroadcasting to {len(target_users)} users with real-time progress tracking..."

        except Exception as e:
            logger.error(f"Error starting gift broadcast: {e}")
            return False, f"Error starting gift broadcast: {e}"

    def _generate_gift_broadcast_message(self, broadcast_data: Dict[str, Any]) -> str:
        """Generate the gift broadcast message"""
        channel_title = broadcast_data.get('channel_title', 'Channel')
        invite_link = broadcast_data.get('invite_link')
        channel_username = broadcast_data.get('channel_username')

        # Determine the channel link
        if invite_link:
            channel_link = invite_link
        elif channel_username:
            # Clean username (remove @ if present)
            clean_username = channel_username.replace('@', '') if channel_username else ''
            channel_link = f"https://t.me/{clean_username}"
        else:
            channel_link = "#"
            logger.warning(f"No valid channel link found for gift broadcast. Channel: {channel_title}, Username: {channel_username}, Invite Link: {invite_link}")

        message = "🎁 <b>EXTRA BONUS</b> 👇!\n\n"
        message += f"👉 <a href=\"{channel_link}\"><b>Click & Join Channel</b></a>\n\n"
        message += "<blockquote>👆 <b>Must Join Above Channel</b> \n"
        message += "<b>Before Click [🎁 Claim Bonus]</b></blockquote>\n\n"
        message += "👇👇👇👇👇"

        return message

    def _generate_gift_broadcast_keyboard(self, broadcast_data: Dict[str, Any]):
        """Generate the gift broadcast keyboard"""
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup

        broadcast_id = broadcast_data['broadcast_id']

        keyboard = InlineKeyboardMarkup([
            [InlineKeyboardButton('🎁 Claim Bonus', callback_data=f'verify_gift_broadcast_{broadcast_id}')]
        ])

        return keyboard

    async def _update_broadcast_stats(self, broadcast_id: str, successful_sends: int, failed_sends: int):
        """Update broadcast statistics"""
        try:
            collection = await get_collection(COLLECTIONS['gift_broadcasts'])
            await collection.update_one(
                {'broadcast_id': broadcast_id},
                {
                    '$set': {
                        'broadcast_sent': True,
                        'successful_sends': successful_sends,
                        'failed_sends': failed_sends,
                        'broadcast_sent_at': get_current_timestamp()
                    }
                }
            )
        except Exception as e:
            logger.error(f"Error updating broadcast stats: {e}")

    async def _execute_gift_broadcast_optimized(self, broadcast_id: str, admin_id: int):
        """Execute gift broadcast using optimized system with real-time progress tracking"""
        try:
            # Get broadcast session
            from services.admin_service import AdminService
            admin_service = AdminService()
            session = await admin_service.get_broadcast_session(broadcast_id)

            if not session:
                logger.error(f"Gift broadcast session {broadcast_id} not found")
                return

            message_data = session["message_data"]
            target_users = session["target_users"]

            logger.info(f"Starting optimized gift broadcast {broadcast_id} to {len(target_users)} users")

            # Send initial progress message to admin
            progress_message_id = await self._send_initial_gift_progress_message(admin_id, broadcast_id, len(target_users))

            # Use optimized broadcast service
            from services.broadcast_service import BroadcastService
            broadcast_service = BroadcastService()
            broadcast_service.set_bot(self.bot)

            # Create progress callback for real-time updates
            async def progress_callback(stats, processed_count, total_users, start_time):
                if progress_message_id:
                    await self._update_gift_progress_message(
                        admin_id, progress_message_id, stats, processed_count, total_users, start_time, broadcast_id
                    )

            # Create broadcast data structure
            broadcast_data = {
                "broadcast_id": broadcast_id,
                "admin_id": admin_id,
                "status": "in_progress",
                "broadcast_data": message_data,
                "total_users": len(target_users),
                "successful_sends": 0,
                "failed_sends": 0,
                "blocked_users": 0,
                "started_at": get_current_timestamp(),
                "completed_at": None
            }

            # Save broadcast log
            collection = await get_collection(COLLECTIONS['broadcast_logs'])
            await collection.insert_one(broadcast_data)

            # Execute optimized broadcast
            result = await broadcast_service.execute_broadcast_optimized(
                broadcast_id, admin_id, progress_callback
            )

            # Send final completion message
            if result["success"]:
                await self._send_gift_broadcast_completion_message(admin_id, broadcast_id, result["statistics"])
            elif result.get("cancelled"):
                await self._send_gift_broadcast_cancellation_message(admin_id, broadcast_id, result["statistics"])
            else:
                await self._send_gift_broadcast_error_message(admin_id, broadcast_id, result["error"])

        except Exception as e:
            logger.error(f"Error executing optimized gift broadcast {broadcast_id}: {e}")
            await self._send_gift_broadcast_error_message(admin_id, broadcast_id, str(e))

    async def _send_initial_gift_progress_message(self, admin_id: int, broadcast_id: str, total_users: int) -> int:
        """Send initial gift broadcast progress message and return message ID"""
        try:
            from telegram import Bot
            from config.settings import settings

            bot = Bot(settings.BOT_TOKEN)

            # Create initial progress message
            progress_message = f"🎁 <b>Gift Broadcast Progress</b>\n\n"
            progress_message += f"🆔 <b>ID:</b> <code>{broadcast_id[-8:]}</code>\n"
            progress_message += f"📈 <b>Progress:</b> 0/{total_users:,} (0.0%)\n"
            progress_message += f"✅ <b>Sent:</b> 0\n"
            progress_message += f"❌ <b>Failed:</b> 0\n"
            progress_message += f"🚫 <b>Blocked:</b> 0\n"
            progress_message += f"💀 <b>Deactivated:</b> 0\n"
            progress_message += f"🔍 <b>Not Found:</b> 0\n"
            progress_message += f"⚡ <b>Rate Limited:</b> 0\n"
            progress_message += f"🧹 <b>Cleaned:</b> 0\n\n"
            progress_message += f"⏱️ <b>ETA:</b> Calculating...\n\n"
            progress_message += "█░░░░░░░░░░░░░░░░░░░\n\n"
            progress_message += "💡 Send /cancel to stop the broadcast"

            # Send message and get the message object
            message = await bot.send_message(
                chat_id=admin_id,
                text=progress_message,
                parse_mode='HTML'
            )

            # Return the message ID for future edits
            return message.message_id

        except Exception as e:
            logger.error(f"Error sending initial gift progress message: {e}")
            return None

    async def _update_gift_progress_message(self, admin_id: int, message_id: int, stats: Dict, processed: int, total: int, start_time: str, broadcast_id: str):
        """Update gift broadcast progress message with real-time statistics"""
        try:
            from telegram import Bot
            from config.settings import settings

            bot = Bot(settings.BOT_TOKEN)

            # Calculate progress percentage
            progress_percent = round((processed / total) * 100, 1) if total > 0 else 0

            # Calculate estimated time remaining
            elapsed_time = get_current_timestamp() - start_time
            if processed > 0:
                avg_time_per_user = elapsed_time / processed
                remaining_users = total - processed
                eta_seconds = avg_time_per_user * remaining_users
                eta_minutes = int(eta_seconds / 60)
                eta_text = f"{eta_minutes}m {int(eta_seconds % 60)}s" if eta_minutes > 0 else f"{int(eta_seconds)}s"
            else:
                eta_text = "Calculating..."

            # Create progress bar
            progress_bar_length = 20
            filled = int((progress_percent / 100) * progress_bar_length)
            empty = progress_bar_length - filled
            progress_bar = "█" * filled + "░" * empty

            # Create progress message
            progress_message = f"🎁 <b>Gift Broadcast Progress</b>\n\n"
            progress_message += f"🆔 <b>ID:</b> <code>{broadcast_id[-8:]}</code>\n"
            progress_message += f"📈 <b>Progress:</b> {processed:,}/{total:,} ({progress_percent}%)\n"
            progress_message += f"✅ <b>Sent:</b> {stats['successful_sends']:,}\n"
            progress_message += f"❌ <b>Failed:</b> {stats['failed_sends']:,}\n"
            progress_message += f"🚫 <b>Blocked:</b> {stats['blocked_users']:,}\n"
            progress_message += f"💀 <b>Deactivated:</b> {stats['deactivated_users']:,}\n"
            progress_message += f"🔍 <b>Not Found:</b> {stats['chat_not_found']:,}\n"
            progress_message += f"⚡ <b>Rate Limited:</b> {stats['rate_limited']:,}\n"
            progress_message += f"🧹 <b>Cleaned:</b> {len(stats['cleaned_users']):,}\n\n"
            progress_message += f"⏱️ <b>ETA:</b> {eta_text}\n\n"
            progress_message += f"{progress_bar}\n\n"
            progress_message += "💡 Send /cancel to stop the broadcast"

            await bot.edit_message_text(
                chat_id=admin_id,
                message_id=message_id,
                text=progress_message,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error updating gift progress message: {e}")

    async def _send_gift_broadcast_completion_message(self, admin_id: int, broadcast_id: str, stats: Dict):
        """Send gift broadcast completion message with detailed statistics"""
        try:
            from telegram import Bot
            from config.settings import settings

            bot = Bot(settings.BOT_TOKEN)

            # Calculate success rate
            total_attempted = stats["successful_sends"] + stats["failed_sends"] + stats["blocked_users"] + stats["deactivated_users"] + stats["chat_not_found"] + stats["other_errors"]
            success_rate = round((stats["successful_sends"] / total_attempted) * 100, 2) if total_attempted > 0 else 0

            completion_message = f"🎁✅ <b>Gift Broadcast Completed Successfully!</b>\n\n"
            completion_message += f"🆔 <b>Broadcast ID:</b> {broadcast_id[-8:]}\n"
            completion_message += f"👥 <b>Total Users:</b> {stats['total_users']:,}\n\n"
            completion_message += f"📊 <b>Detailed Statistics:</b>\n"
            completion_message += f"✅ Successfully Sent: {stats['successful_sends']:,}\n"
            completion_message += f"❌ Failed Sends: {stats['failed_sends']:,}\n"
            completion_message += f"🚫 Blocked Users: {stats['blocked_users']:,}\n"
            completion_message += f"💀 Deactivated: {stats['deactivated_users']:,}\n"
            completion_message += f"🔍 Chat Not Found: {stats['chat_not_found']:,}\n"
            completion_message += f"⚡ Rate Limited: {stats['rate_limited']:,}\n"
            completion_message += f"🔧 Other Errors: {stats['other_errors']:,}\n\n"
            completion_message += f"🧹 <b>Database Cleanup:</b>\n"
            completion_message += f"Cleaned {len(stats['cleaned_users']):,} inactive users\n\n"
            completion_message += f"📈 <b>Success Rate:</b> {success_rate}%\n"
            completion_message += f"🎉 <b>Gift broadcast completed successfully!</b>\n\n"
            completion_message += f"💰 Users can now claim their rewards by joining the channel and clicking the claim button!"

            await bot.send_message(
                chat_id=admin_id,
                text=completion_message,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error sending gift broadcast completion message: {e}")

    async def _send_gift_broadcast_cancellation_message(self, admin_id: int, broadcast_id: str, stats: Dict):
        """Send gift broadcast cancellation message"""
        try:
            from telegram import Bot
            from config.settings import settings

            bot = Bot(settings.BOT_TOKEN)

            cancellation_message = f"🎁🚫 <b>Gift Broadcast Cancelled</b>\n\n"
            cancellation_message += f"🆔 <b>Broadcast ID:</b> {broadcast_id[-8:]}\n"
            cancellation_message += f"👥 <b>Total Users:</b> {stats['total_users']:,}\n\n"
            cancellation_message += f"📊 <b>Final Statistics:</b>\n"
            cancellation_message += f"✅ Successfully Sent: {stats['successful_sends']:,}\n"
            cancellation_message += f"❌ Failed Sends: {stats['failed_sends']:,}\n"
            cancellation_message += f"🚫 Blocked Users: {stats['blocked_users']:,}\n"
            cancellation_message += f"💀 Deactivated: {stats['deactivated_users']:,}\n"
            cancellation_message += f"🔍 Chat Not Found: {stats['chat_not_found']:,}\n"
            cancellation_message += f"⚡ Rate Limited: {stats['rate_limited']:,}\n"
            cancellation_message += f"🧹 Cleaned: {len(stats['cleaned_users']):,}\n\n"
            cancellation_message += f"🛑 <b>Status:</b> Stopped by admin\n"
            cancellation_message += f"💡 The broadcast was cancelled and stopped successfully."

            await bot.send_message(
                chat_id=admin_id,
                text=cancellation_message,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error sending gift broadcast cancellation message: {e}")

    async def _send_gift_broadcast_error_message(self, admin_id: int, broadcast_id: str, error: str):
        """Send gift broadcast error message"""
        try:
            from telegram import Bot
            from config.settings import settings

            bot = Bot(settings.BOT_TOKEN)

            error_message = f"🎁❌ <b>Gift Broadcast Error</b>\n\n"
            error_message += f"🆔 <b>Broadcast ID:</b> {broadcast_id[-8:]}\n"
            error_message += f"❌ <b>Error:</b> {error}\n\n"
            error_message += f"💡 Please try again or contact support if the issue persists."

            await bot.send_message(
                chat_id=admin_id,
                text=error_message,
                parse_mode='HTML'
            )

        except Exception as e:
            logger.error(f"Error sending gift broadcast error message: {e}")
