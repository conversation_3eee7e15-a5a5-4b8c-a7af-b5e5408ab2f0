"""
Broadcast Performance Configuration
Optimized settings for different user scales and Telegram API limits
"""

import logging
from typing import Dict, Any

logger = logging.getLogger(__name__)

class BroadcastConfig:
    """Configuration class for broadcast performance optimization"""
    
    # Telegram API Rate Limits (official limits with safety margins)
    TELEGRAM_RATE_LIMITS = {
        "official_limit_per_second": 30,  # Telegram's official limit
        "safe_limit_per_second": 20,      # Safe limit with margin
        "recommended_limit_per_second": 15,  # Conservative recommended limit
        "messages_per_minute": 1200,      # 20 * 60 = 1200 per minute (safe)
        "burst_limit": 20,                # Conservative burst limit
        "retry_after_seconds": 1,         # Wait time after rate limit hit
    }
    
    # Performance configurations for different user scales (Telegram API compliant)
    PERFORMANCE_CONFIGS = {
        "small": {  # Up to 10,000 users
            "batch_size": 20,                # Smaller batches for safety
            "concurrent_batches": 1,         # Single batch to avoid rate limits
            "delay_between_messages": 0.05,  # 50ms between messages (20 msg/sec)
            "delay_between_batches": 1.0,    # 1 second between batches
            "progress_update_interval": 200,
            "target_rate_per_second": 20,    # Target 20 msg/sec (safe limit)
        },
        "medium": {  # 10,000 to 50,000 users
            "batch_size": 20,                # Smaller batches for safety
            "concurrent_batches": 1,         # Single batch to avoid rate limits
            "delay_between_messages": 0.05,  # 50ms between messages (20 msg/sec)
            "delay_between_batches": 1.0,    # 1 second between batches
            "progress_update_interval": 500,
            "target_rate_per_second": 20,    # Target 20 msg/sec (safe limit)
        },
        "large": {  # 50,000 to 100,000 users
            "batch_size": 15,                # Even smaller batches for large scale
            "concurrent_batches": 1,         # Single batch to avoid rate limits
            "delay_between_messages": 0.067, # 67ms between messages (15 msg/sec)
            "delay_between_batches": 1.0,    # 1 second between batches
            "progress_update_interval": 1000,
            "target_rate_per_second": 15,    # Conservative 15 msg/sec for large broadcasts
        },
        "xlarge": {  # 100,000+ users
            "batch_size": 10,                # Very small batches for massive scale
            "concurrent_batches": 1,         # Single batch to avoid rate limits
            "delay_between_messages": 0.1,   # 100ms between messages (10 msg/sec)
            "delay_between_batches": 1.0,    # 1 second between batches
            "progress_update_interval": 2000,
            "target_rate_per_second": 10,    # Very conservative 10 msg/sec for massive broadcasts
        }
    }
    
    @classmethod
    def get_config_for_user_count(cls, user_count: int) -> Dict[str, Any]:
        """Get optimal configuration based on user count"""
        if user_count <= 10000:
            config_name = "small"
        elif user_count <= 50000:
            config_name = "medium"
        elif user_count <= 100000:
            config_name = "large"
        else:
            config_name = "xlarge"
        
        config = cls.PERFORMANCE_CONFIGS[config_name].copy()
        config["config_name"] = config_name
        config["user_count"] = user_count
        
        # Calculate realistic performance metrics based on target rate
        target_rate = config["target_rate_per_second"]

        # Ensure we don't exceed Telegram's safe limits
        safe_limit = cls.TELEGRAM_RATE_LIMITS["safe_limit_per_second"]
        actual_rate = min(target_rate, safe_limit)

        config["actual_messages_per_second"] = actual_rate
        config["estimated_completion_time_minutes"] = round(user_count / (actual_rate * 60), 2)
        config["estimated_completion_time_hours"] = round(config["estimated_completion_time_minutes"] / 60, 2)
        
        logger.info(f"Selected {config_name} config for {user_count} users: "
                   f"{config['actual_messages_per_second']} msg/sec (Telegram API compliant), "
                   f"~{config['estimated_completion_time_minutes']} minutes "
                   f"({config['estimated_completion_time_hours']} hours)")
        
        return config
    
    @classmethod
    def get_performance_expectations(cls) -> Dict[str, Dict[str, Any]]:
        """Get performance expectations for different user counts"""
        expectations = {}
        
        test_counts = [1000, 5000, 10000, 25000, 50000, 75000, 100000, 200000]
        
        for count in test_counts:
            config = cls.get_config_for_user_count(count)
            expectations[f"{count}_users"] = {
                "user_count": count,
                "config_used": config["config_name"],
                "estimated_time_minutes": config["estimated_completion_time_minutes"],
                "estimated_time_hours": config["estimated_completion_time_hours"],
                "messages_per_second": config["actual_messages_per_second"],
                "target_rate": config["target_rate_per_second"],
                "concurrent_batches": config["concurrent_batches"],
                "batch_size": config["batch_size"],
                "telegram_api_compliant": True
            }
        
        return expectations

class BroadcastPerformanceMonitor:
    """Monitor and track broadcast performance metrics"""
    
    def __init__(self):
        self.start_time = None
        self.processed_count = 0
        self.total_users = 0
        self.successful_sends = 0
        self.failed_sends = 0
        self.current_rate = 0.0
        
    def start_monitoring(self, total_users: int):
        """Start performance monitoring"""
        import time
        self.start_time = time.time()
        self.total_users = total_users
        self.processed_count = 0
        self.successful_sends = 0
        self.failed_sends = 0
        
    def update_progress(self, processed: int, successful: int, failed: int):
        """Update progress metrics"""
        import time
        if not self.start_time:
            return
            
        self.processed_count = processed
        self.successful_sends = successful
        self.failed_sends = failed
        
        elapsed_time = time.time() - self.start_time
        if elapsed_time > 0:
            self.current_rate = processed / elapsed_time
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics"""
        import time
        if not self.start_time:
            return {}
            
        elapsed_time = time.time() - self.start_time
        progress_percentage = (self.processed_count / self.total_users * 100) if self.total_users > 0 else 0
        
        remaining_users = self.total_users - self.processed_count
        estimated_remaining_time = (remaining_users / self.current_rate) if self.current_rate > 0 else 0
        
        return {
            "elapsed_time_seconds": round(elapsed_time, 2),
            "elapsed_time_minutes": round(elapsed_time / 60, 2),
            "processed_count": self.processed_count,
            "total_users": self.total_users,
            "progress_percentage": round(progress_percentage, 2),
            "current_rate_per_second": round(self.current_rate, 2),
            "current_rate_per_minute": round(self.current_rate * 60, 2),
            "successful_sends": self.successful_sends,
            "failed_sends": self.failed_sends,
            "success_rate_percentage": round((self.successful_sends / self.processed_count * 100) if self.processed_count > 0 else 0, 2),
            "estimated_remaining_time_minutes": round(estimated_remaining_time / 60, 2),
            "estimated_total_time_minutes": round((elapsed_time + estimated_remaining_time) / 60, 2)
        }
    
    def log_performance_summary(self):
        """Log final performance summary"""
        stats = self.get_performance_stats()
        logger.info(f"Broadcast Performance Summary:")
        logger.info(f"  Total Users: {stats.get('total_users', 0)}")
        logger.info(f"  Processed: {stats.get('processed_count', 0)}")
        logger.info(f"  Success Rate: {stats.get('success_rate_percentage', 0)}%")
        logger.info(f"  Average Rate: {stats.get('current_rate_per_second', 0)} msg/sec")
        logger.info(f"  Total Time: {stats.get('elapsed_time_minutes', 0)} minutes")

# Global performance monitor instance
performance_monitor = BroadcastPerformanceMonitor()
